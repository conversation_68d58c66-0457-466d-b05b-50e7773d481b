import { log } from './logger';

export interface EPGProgram {
  id: string;
  channelId: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  category?: string;
  language?: string;
}

export interface EPGChannel {
  id: string;
  name: string;
  programs: EPGProgram[];
}

export class EPGManager {
  private static epgData: Map<string, EPGChannel> = new Map();

  /**
   * EPG verilerini y<PERSON>kle (XML formatından)
   */
  static async loadEPGFromXML(xmlUrl: string): Promise<void> {
    const startTime = Date.now();

    try {
      log.apiCall(xmlUrl, 'GET');

      const response = await fetch(xmlUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'NSM IPTV Player/1.0',
          'Accept': 'application/xml, text/xml',
        },
      });

      log.apiResponse(xmlUrl, response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const xmlText = await response.text();
      await this.parseEPGXML(xmlText);

      log.performance('EPG XML loading', Date.now() - startTime);

    } catch (error) {
      log.error('EPG loading failed', { url: xmlUrl, error: error instanceof Error ? error.message : error }, 'EPG_MANAGER');
      // Re-throw the original error or create a new one with more context
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`EPG yüklenirken hata: ${String(error)}`);
    }
  }

  /**
   * EPG XML'ini parse et
   */
  private static async parseEPGXML(xmlText: string): Promise<void> {
    try {
      // Basit XML parsing (gerçek uygulamada xml2js gibi bir kütüphane kullanılmalı)
      const channels = this.extractChannelsFromXML(xmlText);
      const programs = this.extractProgramsFromXML(xmlText);

      // Kanalları ve programları organize et
      channels.forEach(channel => {
        const channelPrograms = programs.filter(program => program.channelId === channel.id);
        this.epgData.set(channel.id, {
          ...channel,
          programs: channelPrograms.sort((a, b) => a.startTime.getTime() - b.startTime.getTime())
        });
      });

      log.info('EPG data parsed successfully', {
        channelCount: channels.length,
        programCount: programs.length
      }, 'EPG_MANAGER');
    } catch (error) {
      log.error('EPG parsing failed', { error: error instanceof Error ? error.message : error }, 'EPG_MANAGER');
      throw error;
    }
  }

  /**
   * XML'den kanalları çıkar
   */
  private static extractChannelsFromXML(xmlText: string): EPGChannel[] {
    const channels: EPGChannel[] = [];
    
    // <channel> taglerini bul
    const channelRegex = /<channel\s+id="([^"]+)"[^>]*>(.*?)<\/channel>/gs;
    let match;

    while ((match = channelRegex.exec(xmlText)) !== null) {
      const channelId = match[1];
      const channelContent = match[2];
      
      // Display name'i bul
      const nameMatch = channelContent.match(/<display-name[^>]*>([^<]+)<\/display-name>/);
      const channelName = nameMatch ? nameMatch[1] : channelId;

      channels.push({
        id: channelId,
        name: channelName,
        programs: []
      });
    }

    return channels;
  }

  /**
   * XML'den programları çıkar
   */
  private static extractProgramsFromXML(xmlText: string): EPGProgram[] {
    const programs: EPGProgram[] = [];
    
    // <programme> taglerini bul
    const programRegex = /<programme\s+start="([^"]+)"\s+stop="([^"]+)"\s+channel="([^"]+)"[^>]*>(.*?)<\/programme>/gs;
    let match;

    while ((match = programRegex.exec(xmlText)) !== null) {
      const startTimeStr = match[1];
      const stopTimeStr = match[2];
      const channelId = match[3];
      const programContent = match[4];

      // Zamanları parse et (XMLTV formatı: YYYYMMDDHHMMSS +ZZZZ)
      const startTime = this.parseXMLTVTime(startTimeStr);
      const endTime = this.parseXMLTVTime(stopTimeStr);

      if (!startTime || !endTime) continue;

      // Title'ı bul
      const titleMatch = programContent.match(/<title[^>]*>([^<]+)<\/title>/);
      const title = titleMatch ? titleMatch[1] : 'Bilinmeyen Program';

      // Description'ı bul
      const descMatch = programContent.match(/<desc[^>]*>([^<]+)<\/desc>/);
      const description = descMatch ? descMatch[1] : undefined;

      // Category'yi bul
      const categoryMatch = programContent.match(/<category[^>]*>([^<]+)<\/category>/);
      const category = categoryMatch ? categoryMatch[1] : undefined;

      programs.push({
        id: `${channelId}_${startTime.getTime()}`,
        channelId,
        title,
        description,
        startTime,
        endTime,
        category
      });
    }

    return programs;
  }

  /**
   * XMLTV zaman formatını Date'e çevir
   */
  private static parseXMLTVTime(timeStr: string): Date | null {
    try {
      // Format: YYYYMMDDHHMMSS +ZZZZ
      const match = timeStr.match(/^(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})\s*([+-]\d{4})?$/);
      if (!match) return null;

      const [, year, month, day, hour, minute, second, timezone] = match;
      
      const date = new Date(
        parseInt(year),
        parseInt(month) - 1, // Month is 0-indexed
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      );

      // Timezone offset'i uygula (basit implementasyon)
      if (timezone) {
        const offsetHours = parseInt(timezone.substring(1, 3));
        const offsetMinutes = parseInt(timezone.substring(3, 5));
        const totalOffsetMinutes = (timezone.startsWith('+') ? -1 : 1) * (offsetHours * 60 + offsetMinutes);
        date.setMinutes(date.getMinutes() + totalOffsetMinutes);
      }

      return date;
    } catch (error) {
      log.warn('Time parsing failed', { timeStr, error: error instanceof Error ? error.message : error }, 'EPG_MANAGER');
      return null;
    }
  }

  /**
   * Kanal için mevcut programı getir
   */
  static getCurrentProgram(channelId: string): EPGProgram | null {
    const channel = this.epgData.get(channelId);
    if (!channel) return null;

    const now = new Date();
    return channel.programs.find(program => 
      program.startTime <= now && program.endTime > now
    ) || null;
  }

  /**
   * Kanal için sonraki programı getir
   */
  static getNextProgram(channelId: string): EPGProgram | null {
    const channel = this.epgData.get(channelId);
    if (!channel) return null;

    const now = new Date();
    return channel.programs.find(program => 
      program.startTime > now
    ) || null;
  }

  /**
   * Kanal için günlük programları getir
   */
  static getDailyPrograms(channelId: string, date: Date = new Date()): EPGProgram[] {
    const channel = this.epgData.get(channelId);
    if (!channel) return [];

    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    return channel.programs.filter(program => 
      program.startTime >= startOfDay && program.startTime <= endOfDay
    );
  }

  /**
   * Tüm kanallar için mevcut programları getir
   */
  static getAllCurrentPrograms(): Map<string, EPGProgram> {
    const currentPrograms = new Map<string, EPGProgram>();
    
    this.epgData.forEach((channel, channelId) => {
      const currentProgram = this.getCurrentProgram(channelId);
      if (currentProgram) {
        currentPrograms.set(channelId, currentProgram);
      }
    });

    return currentPrograms;
  }

  /**
   * EPG verilerini temizle
   */
  static clearEPGData(): void {
    this.epgData.clear();
  }

  /**
   * EPG verilerinin yüklenip yüklenmediğini kontrol et
   */
  static hasEPGData(): boolean {
    return this.epgData.size > 0;
  }

  /**
   * Belirli bir kanal için EPG verisi var mı kontrol et
   */
  static hasChannelEPG(channelId: string): boolean {
    return this.epgData.has(channelId);
  }

  /**
   * Program süresini formatla
   */
  static formatProgramDuration(program: EPGProgram): string {
    const durationMs = program.endTime.getTime() - program.startTime.getTime();
    const durationMinutes = Math.floor(durationMs / (1000 * 60));
    
    if (durationMinutes < 60) {
      return `${durationMinutes} dk`;
    } else {
      const hours = Math.floor(durationMinutes / 60);
      const minutes = durationMinutes % 60;
      return minutes > 0 ? `${hours}s ${minutes}dk` : `${hours}s`;
    }
  }

  /**
   * Program zamanını formatla
   */
  static formatProgramTime(date: Date): string {
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Program tarihini formatla
   */
  static formatProgramDate(date: Date): string {
    return date.toLocaleDateString('tr-TR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
