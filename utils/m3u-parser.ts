import { log } from './logger';

export interface Channel {
  id: string;
  name: string;
  url: string;
  logo?: string;
  group?: string;
  tvgId?: string;
  tvgName?: string;
  tvgLogo?: string;
  tvgShift?: string;
  radioStation?: boolean;
  duration?: number;
}

export interface M3UPlaylist {
  channels: Channel[];
  groups: string[];
  totalChannels: number;
}

export class M3UParser {
  /**
   * M3U içeriğini parse eder ve kanal listesi döndürür
   */
  static async parseM3U(content: string): Promise<M3UPlaylist> {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    const channels: Channel[] = [];
    const groups = new Set<string>();
    
    let currentChannel: Partial<Channel> = {};
    let channelId = 1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // M3U header kontrolü
      if (line.startsWith('#EXTM3U')) {
        continue;
      }

      // Kanal bilgisi satırı
      if (line.startsWith('#EXTINF:')) {
        currentChannel = this.parseExtInf(line);
        currentChannel.id = `channel_${channelId++}`;
      }
      // URL satırı
      else if (!line.startsWith('#') && line.includes('://')) {
        if (currentChannel.name) {
          currentChannel.url = line;
          
          const channel: Channel = {
            id: currentChannel.id!,
            name: currentChannel.name,
            url: currentChannel.url,
            logo: currentChannel.logo,
            group: currentChannel.group,
            tvgId: currentChannel.tvgId,
            tvgName: currentChannel.tvgName,
            tvgLogo: currentChannel.tvgLogo,
            tvgShift: currentChannel.tvgShift,
            radioStation: currentChannel.radioStation || false,
            duration: currentChannel.duration
          };

          channels.push(channel);
          
          if (channel.group) {
            groups.add(channel.group);
          }
          
          currentChannel = {};
        }
      }
    }

    return {
      channels,
      groups: Array.from(groups).sort(),
      totalChannels: channels.length
    };
  }

  /**
   * #EXTINF satırını parse eder
   */
  private static parseExtInf(line: string): Partial<Channel> {
    const channel: Partial<Channel> = {};
    
    // Duration çıkarma
    const durationMatch = line.match(/#EXTINF:([^,]+)/);
    if (durationMatch) {
      const duration = parseFloat(durationMatch[1]);
      if (!isNaN(duration)) {
        channel.duration = duration;
      }
    }

    // Kanal adını çıkarma (virgülden sonraki kısım)
    const nameMatch = line.match(/,(.+)$/);
    if (nameMatch) {
      channel.name = nameMatch[1].trim();
    }

    // Parametreleri parse etme
    const params = this.extractParameters(line);
    
    if (params['tvg-id']) channel.tvgId = params['tvg-id'];
    if (params['tvg-name']) channel.tvgName = params['tvg-name'];
    if (params['tvg-logo']) channel.tvgLogo = params['tvg-logo'];
    if (params['tvg-shift']) channel.tvgShift = params['tvg-shift'];
    if (params['group-title']) channel.group = params['group-title'];
    if (params['radio']) channel.radioStation = params['radio'].toLowerCase() === 'true';
    
    // Logo için tvg-logo'yu öncelikle kullan
    channel.logo = channel.tvgLogo;

    return channel;
  }

  /**
   * EXTINF satırından parametreleri çıkarır
   */
  private static extractParameters(line: string): Record<string, string> {
    const params: Record<string, string> = {};
    
    // Parametreleri bul (key="value" formatında)
    const paramRegex = /(\w+(?:-\w+)*)="([^"]*)"/g;
    let match;
    
    while ((match = paramRegex.exec(line)) !== null) {
      params[match[1]] = match[2];
    }

    return params;
  }

  /**
   * URL'den M3U içeriğini fetch eder ve parse eder
   */
  static async fetchAndParseM3U(url: string): Promise<M3UPlaylist> {
    const startTime = Date.now();

    try {
      log.apiCall(url, 'GET');

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'NSM IPTV Player/1.0',
          'Accept': '*/*',
        },
      });

      log.apiResponse(url, response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const content = await response.text();
      log.debug('M3U content received', { contentLength: content.length }, 'M3U_PARSER');

      if (!content.includes('#EXTM3U') && !content.includes('#EXTINF')) {
        throw new Error('Geçersiz M3U formatı');
      }

      const result = await this.parseM3U(content);
      log.performance('M3U fetch and parse', Date.now() - startTime);

      return result;
    } catch (error) {
      log.error('M3U fetch/parse failed', { url, error: error instanceof Error ? error.message : error }, 'M3U_PARSER');
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`M3U yüklenirken hata: ${String(error)}`);
    }
  }

  /**
   * Kanalları gruplara göre organize eder
   */
  static organizeChannelsByGroup(channels: Channel[]): Record<string, Channel[]> {
    const organized: Record<string, Channel[]> = {};
    
    channels.forEach(channel => {
      const group = channel.group || 'Diğer';
      if (!organized[group]) {
        organized[group] = [];
      }
      organized[group].push(channel);
    });

    return organized;
  }

  /**
   * Kanal arama fonksiyonu
   */
  static searchChannels(channels: Channel[], query: string): Channel[] {
    const searchTerm = query.toLowerCase().trim();
    
    if (!searchTerm) {
      return channels;
    }

    return channels.filter(channel => 
      channel.name.toLowerCase().includes(searchTerm) ||
      (channel.group && channel.group.toLowerCase().includes(searchTerm)) ||
      (channel.tvgName && channel.tvgName.toLowerCase().includes(searchTerm))
    );
  }

  /**
   * Kanalları gruba göre filtreler
   */
  static filterChannelsByGroup(channels: Channel[], group: string): Channel[] {
    if (group === 'Tümü' || !group) {
      return channels;
    }
    
    return channels.filter(channel => channel.group === group);
  }

  /**
   * M3U içeriğinin geçerli olup olmadığını kontrol eder
   */
  static validateM3UContent(content: string): boolean {
    if (!content || content.trim().length === 0) {
      return false;
    }

    // En az #EXTM3U veya #EXTINF içermeli
    return content.includes('#EXTM3U') || content.includes('#EXTINF');
  }

  /**
   * URL'nin geçerli bir M3U URL'si olup olmadığını kontrol eder
   */
  static validateM3UUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }
}
