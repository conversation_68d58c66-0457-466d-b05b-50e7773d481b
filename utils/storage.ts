import * as SQLite from 'expo-sqlite';
import type {Channel} from './m3u-parser';
import type {XtreamCredentials} from './xtream-api';
import {log} from './logger';

export interface Playlist {
  id: string;
  name: string;
  url: string;
  type: 'm3u' | 'xtream';
  createdAt: string;
  channelCount?: number;
  lastUpdated?: string;
  xtreamCredentials?: XtreamCredentials;
}

export interface FavoriteChannel {
  id: string;
  channelId: string;
  playlistId: string;
  channelName: string;
  channelUrl: string;
  channelLogo?: string;
  addedAt: string;
}

export interface RecentlyWatched {
  id: string;
  channelId: string;
  playlistId: string;
  channelName: string;
  channelUrl: string;
  channelLogo?: string;
  watchedAt: string;
  duration?: number;
}

export interface UserPreferences {
  videoQuality: 'auto' | 'high' | 'medium' | 'low';
  autoPlay: boolean;
  showChannelLogos: boolean;
  gridColumns: number;
  keepScreenAwake: boolean;
  defaultVolume: number;
  showEPG: boolean;
  epgLanguage: string;
  theme: 'dark' | 'light';
  fullscreenOrientation: 'auto' | 'landscape' | 'portrait';
}

export interface CategoryFilters {
  playlistId: string;
  selectedCategories: string[];
  selectedGroup: string;
}

// Database instance cache
let dbInstance: SQLite.SQLiteDatabase | null = null;
let isInitializing = false;

// Get database instance with proper initialization
async function getDatabase(): Promise<SQLite.SQLiteDatabase> {
    if (dbInstance) {
        return dbInstance;
    }

    // Prevent multiple initialization attempts
    if (isInitializing) {
        // Wait for initialization to complete
        while (isInitializing) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        if (dbInstance) {
            return dbInstance;
        }
    }

    isInitializing = true;

    try {
        log.info('Initializing SQLite database', {}, 'STORAGE');

        dbInstance = await SQLite.openDatabaseAsync('iptv_player.db');

        // Enable WAL mode and foreign keys
        await dbInstance.execAsync('PRAGMA journal_mode = WAL;');
        await dbInstance.execAsync('PRAGMA foreign_keys = ON;');

        // Check if tables exist
        const tableCheck = await dbInstance.getFirstAsync<{ count: number }>(
            "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name='playlists'"
        );

        if (!tableCheck || tableCheck.count === 0) {
            log.info('Creating database tables', {}, 'STORAGE');

            // Create tables
            await dbInstance.execAsync(`
                CREATE TABLE IF NOT EXISTS playlists
                (
                    id
                    TEXT
                    PRIMARY
                    KEY,
                    name
                    TEXT
                    NOT
                    NULL,
                    url
                    TEXT
                    NOT
                    NULL,
                    type
                    TEXT
                    NOT
                    NULL
                    CHECK (
                    type
                    IN
                (
                    'm3u',
                    'xtream'
                )),
                    created_at TEXT NOT NULL,
                    channel_count INTEGER DEFAULT 0,
                    last_updated TEXT,
                    xtream_credentials TEXT
                    );

                CREATE TABLE IF NOT EXISTS channels
                (
                    id
                    TEXT
                    NOT
                    NULL,
                    playlist_id
                    TEXT
                    NOT
                    NULL,
                    name
                    TEXT
                    NOT
                    NULL,
                    url
                    TEXT
                    NOT
                    NULL,
                    logo
                    TEXT,
                    group_title
                    TEXT,
                    tvg_id
                    TEXT,
                    tvg_name
                    TEXT,
                    tvg_logo
                    TEXT,
                    data
                    TEXT
                    NOT
                    NULL,
                    PRIMARY
                    KEY
                (
                    id,
                    playlist_id
                ),
                    FOREIGN KEY
                (
                    playlist_id
                ) REFERENCES playlists
                (
                    id
                ) ON DELETE CASCADE
                    );

                CREATE TABLE IF NOT EXISTS favorites
                (
                    id
                    TEXT
                    PRIMARY
                    KEY,
                    channel_id
                    TEXT
                    NOT
                    NULL,
                    playlist_id
                    TEXT
                    NOT
                    NULL,
                    channel_name
                    TEXT
                    NOT
                    NULL,
                    channel_url
                    TEXT
                    NOT
                    NULL,
                    channel_logo
                    TEXT,
                    added_at
                    TEXT
                    NOT
                    NULL,
                    FOREIGN
                    KEY
                (
                    playlist_id
                ) REFERENCES playlists
                (
                    id
                ) ON DELETE CASCADE
                    );

                CREATE TABLE IF NOT EXISTS recently_watched
                (
                    id
                    TEXT
                    PRIMARY
                    KEY,
                    channel_id
                    TEXT
                    NOT
                    NULL,
                    playlist_id
                    TEXT
                    NOT
                    NULL,
                    channel_name
                    TEXT
                    NOT
                    NULL,
                    channel_url
                    TEXT
                    NOT
                    NULL,
                    channel_logo
                    TEXT,
                    watched_at
                    TEXT
                    NOT
                    NULL,
                    duration
                    INTEGER,
                    FOREIGN
                    KEY
                (
                    playlist_id
                ) REFERENCES playlists
                (
                    id
                ) ON DELETE CASCADE
                    );

                CREATE TABLE IF NOT EXISTS user_preferences
                (
                    key
                    TEXT
                    PRIMARY
                    KEY,
                    value
                    TEXT
                    NOT
                    NULL
                );

                CREATE TABLE IF NOT EXISTS category_filters
                (
                    playlist_id
                    TEXT
                    PRIMARY
                    KEY,
                    selected_categories
                    TEXT
                    NOT
                    NULL,
                    selected_group
                    TEXT
                    NOT
                    NULL,
                    FOREIGN
                    KEY
                (
                    playlist_id
                ) REFERENCES playlists
                (
                    id
                ) ON DELETE CASCADE
                    );

                CREATE INDEX IF NOT EXISTS idx_channels_playlist_id ON channels(playlist_id);
                CREATE INDEX IF NOT EXISTS idx_favorites_channel_id ON favorites(channel_id);
                CREATE INDEX IF NOT EXISTS idx_recently_watched_watched_at ON recently_watched(watched_at DESC);
            `);

            log.info('Database tables created successfully', {}, 'STORAGE');
        }

        isInitializing = false;
        log.info('Database initialization completed', {}, 'STORAGE');
        return dbInstance;
    } catch (error) {
        isInitializing = false;
        dbInstance = null;
        log.error('Failed to initialize database', {error: error instanceof Error ? error.message : error}, 'STORAGE');
        throw error;
    }
}



export const PlaylistStorage = {
    // Tüm playlist'leri getir (en yeni eklenen en üstte)
  async getPlaylists(): Promise<Playlist[]> {
    try {
        const database = await getDatabase();
        const rows = await database.getAllAsync<{
            id: string;
            name: string;
            url: string;
            type: 'm3u' | 'xtream';
            created_at: string;
            channel_count: number | null;
            last_updated: string | null;
            xtream_credentials: string | null;
        }>('SELECT * FROM playlists ORDER BY created_at DESC');

        return rows.map(row => ({
            id: row.id,
            name: row.name,
            url: row.url,
            type: row.type,
            createdAt: row.created_at,
            channelCount: row.channel_count || undefined,
            lastUpdated: row.last_updated || undefined,
            xtreamCredentials: row.xtream_credentials ? JSON.parse(row.xtream_credentials) : undefined,
        }));
    } catch (error) {
      log.error('Failed to get playlists', { error: error instanceof Error ? error.message : error }, 'STORAGE');
      return [];
    }
  },

  // Yeni M3U playlist ekle
  async addPlaylist(name: string, url: string): Promise<Playlist> {
    try {
        const database = await getDatabase();
      const newPlaylist: Playlist = {
        id: Date.now().toString(),
        name,
        url,
        type: 'm3u',
        createdAt: new Date().toISOString(),
      };

        await database.runAsync(
            'INSERT INTO playlists (id, name, url, type, created_at) VALUES (?, ?, ?, ?, ?)',
            newPlaylist.id, newPlaylist.name, newPlaylist.url, newPlaylist.type, newPlaylist.createdAt
        );

      return newPlaylist;
    } catch (error) {
      log.error('Failed to add playlist', { name, url, error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  },

  // Yeni Xtream playlist ekle
  async addXtreamPlaylist(name: string, credentials: XtreamCredentials): Promise<Playlist> {
    try {
        const database = await getDatabase();
      const newPlaylist: Playlist = {
        id: Date.now().toString(),
        name,
        url: credentials.serverUrl,
        type: 'xtream',
        createdAt: new Date().toISOString(),
        xtreamCredentials: credentials,
      };

        await database.runAsync(
            'INSERT INTO playlists (id, name, url, type, created_at, xtream_credentials) VALUES (?, ?, ?, ?, ?, ?)',
            newPlaylist.id, newPlaylist.name, newPlaylist.url, newPlaylist.type,
            newPlaylist.createdAt, JSON.stringify(credentials)
        );

      return newPlaylist;
    } catch (error) {
      log.error('Failed to add Xtream playlist', { name, credentials: credentials.serverUrl, error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  },

  // Playlist sil
  async deletePlaylist(id: string): Promise<void> {
    try {
        const database = await getDatabase();

        // Foreign key constraints will automatically delete related data
        await database.runAsync('DELETE FROM playlists WHERE id = ?', id);

        log.info('Deleted playlist and related data', {id}, 'STORAGE');
    } catch (error) {
      log.error('Failed to delete playlist', { id, error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  },

  // Playlist güncelle
  async updatePlaylist(id: string, name: string, url: string): Promise<void> {
    try {
        const database = await getDatabase();
        await database.runAsync(
            'UPDATE playlists SET name = ?, url = ?, last_updated = ? WHERE id = ?',
            name, url, new Date().toISOString(), id
        );
    } catch (error) {
      log.error('Failed to update playlist', { id, name, url, error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  },

  // Playlist kanal sayısını güncelle
  async updatePlaylistChannelCount(id: string, channelCount: number): Promise<void> {
    try {
        const database = await getDatabase();
        await database.runAsync(
            'UPDATE playlists SET channel_count = ?, last_updated = ? WHERE id = ?',
            channelCount, new Date().toISOString(), id
        );
    } catch (error) {
      log.error('Failed to update playlist channel count', { id, channelCount, error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  },

  // Tüm playlist'leri temizle
  async clearAllPlaylists(): Promise<void> {
    try {
        const database = await getDatabase();
        // Foreign key constraints will automatically delete related data
        await database.runAsync('DELETE FROM playlists');
    } catch (error) {
      log.error('Failed to clear all playlists', { error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  }
};

// Kanal Storage
export const ChannelStorage = {
    // Storage boyutunu kontrol et
    async checkStorageSpace(): Promise<{ available: boolean; usedKeys: string[]; totalSize: number; maxSize: number }> {
        try {
            const database = await getDatabase();

            // SQLite veritabanı boyutunu kontrol et
            const result = await database.getFirstAsync<{ page_count: number; page_size: number }>(
                'PRAGMA page_count; PRAGMA page_size;'
            );

            const totalSize = (result?.page_count || 0) * (result?.page_size || 0);
            const maxSize = 100 * 1024 * 1024; // SQLite için 100MB limit (AsyncStorage'dan çok daha büyük)
            const isAvailable = totalSize < maxSize * 0.8;

            const channelCount = await database.getFirstAsync<{ count: number }>(
                'SELECT COUNT(*) as count FROM channels'
            );

            return {
                available: isAvailable,
                usedKeys: [`channels: ${channelCount?.count || 0} records`],
                totalSize,
                maxSize
            };
        } catch (error) {
            log.error('Failed to check storage space', {error: error instanceof Error ? error.message : error}, 'STORAGE');
            return {available: true, usedKeys: [], totalSize: 0, maxSize: 100 * 1024 * 1024};
        }
    },

    // Eski kanal verilerini temizle (SQLite'da foreign key constraints otomatik halleder)
    async cleanupOldChannelData(): Promise<void> {
        try {
            const database = await getDatabase();
            // Foreign key constraints sayesinde orphaned data otomatik temizlenir
            await database.runAsync('VACUUM'); // Database'i optimize et
            log.info('Database cleanup completed', {}, 'STORAGE');
        } catch (error) {
            log.error('Failed to cleanup old channel data', {error: error instanceof Error ? error.message : error}, 'STORAGE');
        }
    },

    // Playlist kanallarını kaydet
  async saveChannels(playlistId: string, channels: Channel[]): Promise<void> {
      try {
          const database = await getDatabase();

          log.info('Saving channels to SQLite', {
              channelCount: channels.length,
              playlistId
          }, 'STORAGE');

          // Transaction kullanarak tüm kanalları kaydet
          await database.withTransactionAsync(async () => {
              // Önce eski kanalları sil
              await database.runAsync('DELETE FROM channels WHERE playlist_id = ?', playlistId);

              // Prepared statement kullanarak kanalları kaydet
              const statement = await database.prepareAsync(
                  'INSERT INTO channels (id, playlist_id, name, url, logo, group_title, tvg_id, tvg_name, tvg_logo, data) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)'
              );

              try {
                  for (const channel of channels) {
                      await statement.executeAsync(
                          channel.id,
                          playlistId,
                          channel.name,
                          channel.url,
                          channel.logo || null,
                          channel.group || null,
                          channel.tvgId || null,
                          channel.tvgName || null,
                          channel.tvgLogo || null,
                          JSON.stringify(channel)
                      );
                  }
              } finally {
                  await statement.finalizeAsync();
              }
          });

          // Playlist kanal sayısını güncelle
          await PlaylistStorage.updatePlaylistChannelCount(playlistId, channels.length);

          log.info('Successfully saved channels to SQLite', {
              playlistId,
              channelCount: channels.length
          }, 'STORAGE');
      } catch (error) {
          log.error('Failed to save channels', {
              playlistId,
              channelCount: channels.length,
              error: error instanceof Error ? error.message : error
          }, 'STORAGE');
          throw error;
    }
  },

    // Playlist kanallarını getir
  async getChannels(playlistId: string): Promise<Channel[]> {
    try {
        const database = await getDatabase();
        const rows = await database.getAllAsync<{
            id: string;
            data: string;
        }>('SELECT id, data FROM channels WHERE playlist_id = ?', playlistId);

        const channels = rows.map(row => JSON.parse(row.data) as Channel);

        log.debug('Loaded channels from SQLite', {
            playlistId,
            totalChannels: channels.length
        }, 'STORAGE');

        return channels;
    } catch (error) {
      log.error('Failed to get channels', { playlistId, error: error instanceof Error ? error.message : error }, 'STORAGE');
      return [];
    }
  },

    // Playlist kanallarını sil
  async deleteChannels(playlistId: string): Promise<void> {
    try {
        const database = await getDatabase();
        const result = await database.runAsync('DELETE FROM channels WHERE playlist_id = ?', playlistId);

        log.info('Deleted channel data', {
            playlistId,
            deletedRows: result.changes
        }, 'STORAGE');
    } catch (error) {
      log.error('Failed to delete channels', { playlistId, error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  },

    // Tüm kanal verilerini temizle
    async clearAllChannels(): Promise<void> {
        try {
            const database = await getDatabase();
            const result = await database.runAsync('DELETE FROM channels');

            log.info('Cleared all channel data', {deletedRows: result.changes}, 'STORAGE');
        } catch (error) {
            log.error('Failed to clear all channels', {error: error instanceof Error ? error.message : error}, 'STORAGE');
            throw error;
        }
    },

    // Storage istatistiklerini getir
    async getStorageStats(): Promise<{
        totalKeys: number;
        channelKeys: number;
        estimatedSize: number;
        estimatedSizeMB: number;
        maxSizeMB: number;
        usagePercentage: number;
        playlistDetails: Array<{ playlistId: string; channelCount: number; sizeMB: number }>;
    }> {
        try {
            const database = await getDatabase();

            // Database boyutunu al
            const sizeResult = await database.getFirstAsync<{ page_count: number; page_size: number }>(
                'PRAGMA page_count'
            );
            const pageSizeResult = await database.getFirstAsync<{ page_size: number }>(
                'PRAGMA page_size'
            );

            const estimatedSize = (sizeResult?.page_count || 0) * (pageSizeResult?.page_size || 0);
            const maxSize = 100 * 1024 * 1024; // 100MB
            const estimatedSizeMB = Math.round(estimatedSize / 1024 / 1024 * 100) / 100;
            const maxSizeMB = Math.round(maxSize / 1024 / 1024 * 100) / 100;
            const usagePercentage = Math.round((estimatedSize / maxSize) * 100);

            // Playlist detaylarını al
            const playlistDetails = await database.getAllAsync<{
                playlist_id: string;
                channel_count: number;
            }>(`
        SELECT
          p.id as playlist_id,
          COUNT(c.id) as channel_count
        FROM playlists p
        LEFT JOIN channels c ON p.id = c.playlist_id
        GROUP BY p.id
        ORDER BY channel_count DESC
      `);

            // Toplam tablo sayılarını al
            const tableStats = await database.getAllAsync<{ name: string; count: number }>(`
        SELECT 'playlists' as name, COUNT(*) as count FROM playlists
        UNION ALL
        SELECT 'channels' as name, COUNT(*) as count FROM channels
        UNION ALL
        SELECT 'favorites' as name, COUNT(*) as count FROM favorites
        UNION ALL
        SELECT 'recently_watched' as name, COUNT(*) as count FROM recently_watched
      `);

            const totalRecords = tableStats.reduce((sum, stat) => sum + stat.count, 0);

            return {
                totalKeys: totalRecords,
                channelKeys: playlistDetails.reduce((sum, detail) => sum + detail.channel_count, 0),
                estimatedSize,
                estimatedSizeMB,
                maxSizeMB,
                usagePercentage,
                playlistDetails: playlistDetails.map(detail => ({
                    playlistId: detail.playlist_id,
                    channelCount: detail.channel_count,
                    sizeMB: Math.round((detail.channel_count * 1000) / 1024 / 1024 * 100) / 100 // Tahmini boyut
                }))
            };
        } catch (error) {
            log.error('Failed to get storage stats', {error: error instanceof Error ? error.message : error}, 'STORAGE');
            return {
                totalKeys: 0,
                channelKeys: 0,
                estimatedSize: 0,
                estimatedSizeMB: 0,
                maxSizeMB: 100,
                usagePercentage: 0,
                playlistDetails: []
            };
        }
  }
};

// Favori Kanallar Storage
export const FavoriteStorage = {
  // Tüm favori kanalları getir
  async getFavorites(): Promise<FavoriteChannel[]> {
    try {
        const database = await getDatabase();
        const rows = await database.getAllAsync<{
            id: string;
            channel_id: string;
            playlist_id: string;
            channel_name: string;
            channel_url: string;
            channel_logo: string | null;
            added_at: string;
        }>('SELECT * FROM favorites ORDER BY added_at DESC');

        return rows.map(row => ({
            id: row.id,
            channelId: row.channel_id,
            playlistId: row.playlist_id,
            channelName: row.channel_name,
            channelUrl: row.channel_url,
            channelLogo: row.channel_logo || undefined,
            addedAt: row.added_at,
        }));
    } catch (error) {
      log.error('Failed to get favorites', { error: error instanceof Error ? error.message : error }, 'STORAGE');
      return [];
    }
  },

  // Favori kanal ekle
  async addFavorite(channel: Channel, playlistId: string): Promise<FavoriteChannel> {
    try {
        const database = await getDatabase();
      const newFavorite: FavoriteChannel = {
        id: Date.now().toString(),
        channelId: channel.id,
        playlistId,
        channelName: channel.name,
        channelUrl: channel.url,
        channelLogo: channel.logo,
        addedAt: new Date().toISOString(),
      };

        await database.runAsync(
            'INSERT INTO favorites (id, channel_id, playlist_id, channel_name, channel_url, channel_logo, added_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
            newFavorite.id, newFavorite.channelId, newFavorite.playlistId,
            newFavorite.channelName, newFavorite.channelUrl, newFavorite.channelLogo || null, newFavorite.addedAt
        );

      return newFavorite;
    } catch (error) {
      log.error('Failed to add favorite', { channelId: channel.id, playlistId, error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  },

  // Favori kanal sil
  async removeFavorite(channelId: string): Promise<void> {
    try {
        const database = await getDatabase();
        await database.runAsync('DELETE FROM favorites WHERE channel_id = ?', channelId);
    } catch (error) {
      log.error('Failed to remove favorite', { channelId, error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  },

  // Kanalın favori olup olmadığını kontrol et
  async isFavorite(channelId: string): Promise<boolean> {
    try {
        const database = await getDatabase();
        const result = await database.getFirstAsync<{ count: number }>(
            'SELECT COUNT(*) as count FROM favorites WHERE channel_id = ?', channelId
        );
        return (result?.count || 0) > 0;
    } catch (error) {
      log.error('Failed to check favorite status', { channelId, error: error instanceof Error ? error.message : error }, 'STORAGE');
      return false;
    }
  },

    // Belirli playlist'e ait favorileri sil
    async removeFavoritesByPlaylistId(playlistId: string): Promise<void> {
        try {
            const database = await getDatabase();
            await database.runAsync('DELETE FROM favorites WHERE playlist_id = ?', playlistId);
        } catch (error) {
            log.error('Failed to remove favorites by playlist ID', {
                playlistId,
                error: error instanceof Error ? error.message : error
            }, 'STORAGE');
            throw error;
        }
    },

  // Tüm favorileri temizle
  async clearAllFavorites(): Promise<void> {
    try {
        const database = await getDatabase();
        await database.runAsync('DELETE FROM favorites');
    } catch (error) {
      log.error('Failed to clear all favorites', { error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  }
};

// Son İzlenen Kanallar Storage
export const RecentlyWatchedStorage = {
  // Son izlenen kanalları getir
  async getRecentlyWatched(): Promise<RecentlyWatched[]> {
    try {
        const database = await getDatabase();
        const rows = await database.getAllAsync<{
            id: string;
            channel_id: string;
            playlist_id: string;
            channel_name: string;
            channel_url: string;
            channel_logo: string | null;
            watched_at: string;
            duration: number | null;
        }>('SELECT * FROM recently_watched ORDER BY watched_at DESC LIMIT 50');

        return rows.map(row => ({
            id: row.id,
            channelId: row.channel_id,
            playlistId: row.playlist_id,
            channelName: row.channel_name,
            channelUrl: row.channel_url,
            channelLogo: row.channel_logo || undefined,
            watchedAt: row.watched_at,
            duration: row.duration || undefined,
        }));
    } catch (error) {
      log.error('Failed to get recently watched', { error: error instanceof Error ? error.message : error }, 'STORAGE');
      return [];
    }
  },

  // Son izlenen kanal ekle
  async addRecentlyWatched(channel: Channel, playlistId: string, duration?: number): Promise<void> {
    try {
        const database = await getDatabase();

        await database.withTransactionAsync(async () => {
            // Aynı kanal varsa kaldır
            await database.runAsync('DELETE FROM recently_watched WHERE channel_id = ?', channel.id);

            const newRecentItem: RecentlyWatched = {
                id: Date.now().toString(),
                channelId: channel.id,
                playlistId,
                channelName: channel.name,
                channelUrl: channel.url,
                channelLogo: channel.logo,
                watchedAt: new Date().toISOString(),
                duration,
            };

            // Yeni kaydı ekle
            await database.runAsync(
                'INSERT INTO recently_watched (id, channel_id, playlist_id, channel_name, channel_url, channel_logo, watched_at, duration) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                newRecentItem.id, newRecentItem.channelId, newRecentItem.playlistId,
                newRecentItem.channelName, newRecentItem.channelUrl, newRecentItem.channelLogo || null,
                newRecentItem.watchedAt, newRecentItem.duration || null
            );

            // 50'den fazla kayıt varsa eski olanları sil
            await database.runAsync(`
          DELETE FROM recently_watched
          WHERE id NOT IN (
            SELECT id FROM recently_watched
            ORDER BY watched_at DESC
            LIMIT 50
          )
        `);
        });
    } catch (error) {
      log.error('Failed to add recently watched', { channelId: channel.id, playlistId, error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  },

    // Tek bir son izlenen kanalı kaldır
    async removeRecentlyWatched(itemId: string): Promise<void> {
        try {
            const database = await getDatabase();
            await database.runAsync('DELETE FROM recently_watched WHERE id = ?', itemId);
        } catch (error) {
            log.error('Failed to remove recently watched item', {
                itemId,
                error: error instanceof Error ? error.message : error
            }, 'STORAGE');
            throw error;
        }
    },

    // Belirli playlist'e ait son izlenenleri sil
    async removeRecentlyWatchedByPlaylistId(playlistId: string): Promise<void> {
        try {
            const database = await getDatabase();
            await database.runAsync('DELETE FROM recently_watched WHERE playlist_id = ?', playlistId);
        } catch (error) {
            log.error('Failed to remove recently watched by playlist ID', {
                playlistId,
                error: error instanceof Error ? error.message : error
            }, 'STORAGE');
            throw error;
        }
    },

  // Son izlenen kanalları temizle
  async clearRecentlyWatched(): Promise<void> {
    try {
        const database = await getDatabase();
        await database.runAsync('DELETE FROM recently_watched');
    } catch (error) {
      log.error('Failed to clear recently watched', { error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  }
};

// Kullanıcı Tercihleri Storage
export const PreferencesStorage = {
  // Varsayılan tercihler
  getDefaultPreferences(): UserPreferences {
    return {
      videoQuality: 'auto',
      autoPlay: true,
      showChannelLogos: true,
      gridColumns: 2,
      keepScreenAwake: true,
      defaultVolume: 0.8,
      showEPG: true,
      epgLanguage: 'tr',
      theme: 'dark',
      fullscreenOrientation: 'landscape',
    };
  },

  // Kullanıcı tercihlerini getir
  async getPreferences(): Promise<UserPreferences> {
    try {
        const database = await getDatabase();
        const rows = await database.getAllAsync<{
            key: string;
            value: string;
        }>('SELECT key, value FROM user_preferences');

        const saved: Partial<UserPreferences> = {};
        for (const row of rows) {
            try {
                saved[row.key as keyof UserPreferences] = JSON.parse(row.value);
            } catch {
                // JSON parse hatası varsa skip et
            }
        }

        // Varsayılan değerlerle birleştir (yeni özellikler için)
        return {...this.getDefaultPreferences(), ...saved};
    } catch (error) {
      log.error('Failed to get preferences', { error: error instanceof Error ? error.message : error }, 'STORAGE');
      return this.getDefaultPreferences();
    }
  },

  // Kullanıcı tercihlerini kaydet
  async savePreferences(preferences: Partial<UserPreferences>): Promise<void> {
    try {
        const database = await getDatabase();

        await database.withTransactionAsync(async () => {
            const statement = await database.prepareAsync(
                'INSERT OR REPLACE INTO user_preferences (key, value) VALUES (?, ?)'
            );

            try {
                for (const [key, value] of Object.entries(preferences)) {
                    await statement.executeAsync(key, JSON.stringify(value));
                }
            } finally {
                await statement.finalizeAsync();
            }
        });
    } catch (error) {
      log.error('Failed to save preferences', { preferences, error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  },

  // Tercihleri sıfırla
  async resetPreferences(): Promise<void> {
    try {
        const database = await getDatabase();
        await database.runAsync('DELETE FROM user_preferences');
    } catch (error) {
      log.error('Failed to reset preferences', { error: error instanceof Error ? error.message : error }, 'STORAGE');
      throw error;
    }
  }
};

// Kategori Filtreleme Storage
export const CategoryFilterStorage = {
  // Kategori filtrelerini getir
  async getCategoryFilters(playlistId: string): Promise<CategoryFilters | null> {
    try {
        const database = await getDatabase();
        const row = await database.getFirstAsync<{
            playlist_id: string;
            selected_categories: string;
            selected_group: string;
        }>('SELECT * FROM category_filters WHERE playlist_id = ?', playlistId);

        if (row) {
            return {
                playlistId: row.playlist_id,
                selectedCategories: JSON.parse(row.selected_categories),
                selectedGroup: row.selected_group,
            };
        }
        return null;
    } catch (error) {
      log.error('Failed to get category filters', {
        playlistId,
        error: error instanceof Error ? error.message : error
      }, 'STORAGE');
      return null;
    }
  },

  // Kategori filtrelerini kaydet
  async saveCategoryFilters(playlistId: string, selectedCategories: string[], selectedGroup: string): Promise<void> {
    try {
        const database = await getDatabase();
        await database.runAsync(
            'INSERT OR REPLACE INTO category_filters (playlist_id, selected_categories, selected_group) VALUES (?, ?, ?)',
            playlistId, JSON.stringify(selectedCategories), selectedGroup
        );
    } catch (error) {
      log.error('Failed to save category filters', {
        playlistId,
        selectedCategories,
        selectedGroup,
        error: error instanceof Error ? error.message : error
      }, 'STORAGE');
      throw error;
    }
  },

  // Kategori filtrelerini temizle
  async clearCategoryFilters(playlistId: string): Promise<void> {
    try {
        const database = await getDatabase();
        await database.runAsync('DELETE FROM category_filters WHERE playlist_id = ?', playlistId);
    } catch (error) {
      log.error('Failed to clear category filters', {
        playlistId,
        error: error instanceof Error ? error.message : error
      }, 'STORAGE');
      throw error;
    }
  }
};
