/**
 * Production-ready logger utility
 * Provides structured logging with different levels and conditional output
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: Date;
  source?: string;
}

class Logger {
  private static instance: Logger;
  private logLevel: LogLevel = __DEV__ ? LogLevel.DEBUG : LogLevel.ERROR;
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  private constructor() {}

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private addLog(level: LogLevel, message: string, data?: any, source?: string): void {
    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: new Date(),
      source,
    };

    this.logs.push(entry);
    
    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    if (this.shouldLog(level)) {
      this.outputLog(entry);
    }
  }

  private outputLog(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const source = entry.source ? `[${entry.source}]` : '';
    const prefix = `${timestamp} ${source}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.log(`🔍 ${prefix}`, entry.message, entry.data || '');
        break;
      case LogLevel.INFO:
        console.info(`ℹ️ ${prefix}`, entry.message, entry.data || '');
        break;
      case LogLevel.WARN:
        console.warn(`⚠️ ${prefix}`, entry.message, entry.data || '');
        break;
      case LogLevel.ERROR:
        console.error(`❌ ${prefix}`, entry.message, entry.data || '');
        break;
    }
  }

  debug(message: string, data?: any, source?: string): void {
    this.addLog(LogLevel.DEBUG, message, data, source);
  }

  info(message: string, data?: any, source?: string): void {
    this.addLog(LogLevel.INFO, message, data, source);
  }

  warn(message: string, data?: any, source?: string): void {
    this.addLog(LogLevel.WARN, message, data, source);
  }

  error(message: string, data?: any, source?: string): void {
    this.addLog(LogLevel.ERROR, message, data, source);
  }

  getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level);
    }
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
  }

  // Convenience methods for common use cases
  apiCall(url: string, method: string = 'GET'): void {
    this.debug(`API Call: ${method} ${url}`, undefined, 'API');
  }

  apiResponse(url: string, status: number, data?: any): void {
    if (status >= 400) {
      this.error(`API Error: ${status} ${url}`, data, 'API');
    } else {
      this.debug(`API Success: ${status} ${url}`, data, 'API');
    }
  }

  userAction(action: string, data?: any): void {
    this.info(`User Action: ${action}`, data, 'USER');
  }

  performance(operation: string, duration: number): void {
    this.debug(`Performance: ${operation} took ${duration}ms`, undefined, 'PERF');
  }
}

// Export singleton instance
export const logger = Logger.getInstance();

// Export convenience functions
export const log = {
  debug: (message: string, data?: any, source?: string) => logger.debug(message, data, source),
  info: (message: string, data?: any, source?: string) => logger.info(message, data, source),
  warn: (message: string, data?: any, source?: string) => logger.warn(message, data, source),
  error: (message: string, data?: any, source?: string) => logger.error(message, data, source),
  apiCall: (url: string, method?: string) => logger.apiCall(url, method),
  apiResponse: (url: string, status: number, data?: any) => logger.apiResponse(url, status, data),
  userAction: (action: string, data?: any) => logger.userAction(action, data),
  performance: (operation: string, duration: number) => logger.performance(operation, duration),
};
