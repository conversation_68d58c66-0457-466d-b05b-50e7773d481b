export interface XtreamCredentials {
  serverUrl: string;
  username: string;
  password: string;
}

export interface XtreamAuthInfo {
  user_info: {
    username: string;
    password: string;
    message: string;
    auth: number;
    status: string;
    exp_date: string;
    is_trial: string;
    active_cons: string;
    created_at: string;
    max_connections: string;
    allowed_output_formats: string[];
  };
  server_info: {
    url: string;
    port: string;
    https_port: string;
    server_protocol: string;
    rtmp_port: string;
    timezone: string;
    timestamp_now: number;
    time_now: string;
  };
}

export interface XtreamChannel {
  num: number;
  name: string;
  stream_type: string;
  stream_id: number;
  stream_icon: string;
  epg_channel_id: string;
  added: string;
  category_name: string;
  category_id: string;
  series_no: null;
  live: string;
  container_extension: string;
  custom_sid: string;
  direct_source: string;
}

export class XtreamAPI {
  private credentials: XtreamCredentials;
  private authInfo: XtreamAuthInfo | null = null;

  constructor(credentials: XtreamCredentials) {
    const normalizedUrl = XtreamAPI.sanitizeServerUrl(credentials.serverUrl);
    this.credentials = {
      ...credentials,
      serverUrl: normalizedUrl
    };
  }

  // Yardımcılar: URL oluşturma ve JSON fetch
  private static readonly defaultHeaders = { Accept: 'application/json' } as const;

  private buildURL(
    action?: string,
    extraParams?: Record<string, string | number | undefined>
  ): string {
    const base = new URL(`${this.credentials.serverUrl}/player_api.php`);
    const params = new URLSearchParams({
      username: this.credentials.username,
      password: this.credentials.password,
    });
    if (action) params.set('action', action);
    if (extraParams) {
      for (const [key, value] of Object.entries(extraParams)) {
        if (value !== undefined && value !== null) params.set(key, String(value));
      }
    }
    base.search = params.toString();
    return base.toString();
  }

  private async getJSON<T>(url: string, context: string): Promise<T> {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: XtreamAPI.defaultHeaders,
      });
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      return (await response.json()) as T;
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      throw new Error(`${context}: ${message}`);
    }
  }


  /**
   * Sunucu ile bağlantıyı test eder ve kullanıcı bilgilerini alır
   */
  async authenticate(): Promise<XtreamAuthInfo> {
    const url = this.buildURL();
    const data = await this.getJSON<XtreamAuthInfo>(url, 'Kimlik doğrulama hatası');

      if (!data.user_info || data.user_info.auth !== 1) {
      throw new Error('Kimlik doğrulama başarısız: Geçersiz kullanıcı adı veya şifre');
    }

      this.authInfo = data;
    return data;
  }

  /**
   * Canlı TV kanallarını getirir
   */
  async getLiveStreams(): Promise<XtreamChannel[]> {
    const url = this.buildURL('get_live_streams');
    const data = await this.getJSON<unknown>(url, 'Canlı kanallar alınırken hata');
    return Array.isArray(data) ? (data as XtreamChannel[]) : [];
  }



  /**
   * Belirli bir kategorideki canlı kanalları getirir
   */
  async getLiveStreamsByCategory(category_id: string | number): Promise<XtreamChannel[]> {
    const url = this.buildURL('get_live_streams', { category_id });
    const data = await this.getJSON<unknown>(url, 'Kategoriye göre kanallar alınırken hata');
    return Array.isArray(data) ? (data as XtreamChannel[]) : [];
  }


  /**
   * Canlı kategorileri (gruplar) listesini getirir
   */
  async getLiveCategories(): Promise<{ category_id: string; category_name: string; parent_id?: string | number }[]> {
    const url = this.buildURL('get_live_categories');
    console.log('Kategori API URL:', url);

    const data = await this.getJSON<unknown>(url, 'Canlı kategoriler alınırken hata');
    console.log('Ham kategori verisi:', data);

    if (!Array.isArray(data)) {
      console.warn('Kategori verisi array değil:', typeof data);
      return [];
    }

    const categories = data as { category_id: string; category_name: string; parent_id?: string | number }[];
    console.log('Parse edilen kategoriler:', categories);

    // Kategorileri temizle ve doğrula
    const cleanedCategories = categories.filter(cat => {
      const hasValidId = cat.category_id !== undefined && cat.category_id !== null && cat.category_id !== '';
      const hasValidName = cat.category_name !== undefined && cat.category_name !== null && cat.category_name !== '';

      if (!hasValidId || !hasValidName) {
        console.warn('Geçersiz kategori filtrelendi:', cat);
        return false;
      }

      return true;
    });

    console.log('Temizlenmiş kategoriler:', cleanedCategories);
    return cleanedCategories;
  }




  /**
   * Kanal için EPG bilgilerini getirir - Birden fazla endpoint dener
   */
  async getEPGForChannel(streamId: number): Promise<any> {
      console.log('🔍 EPG endpoint\'leri deneniyor...');

      // 1. get_short_epg endpoint'i dene
      try {
          const shortEpgUrl = this.buildURL('get_short_epg', {stream_id: streamId});
          console.log('📡 get_short_epg URL:', shortEpgUrl);
          const shortEpgData = await this.getJSON<any>(shortEpgUrl, 'get_short_epg hatası');
          console.log('📺 get_short_epg response:', shortEpgData);

          if (shortEpgData && shortEpgData.epg_listings && shortEpgData.epg_listings.length > 0) {
              console.log('✅ get_short_epg\'de veri bulundu');
              return shortEpgData;
          }
      } catch (error) {
          console.log('❌ get_short_epg hatası:', error);
      }

      // 2. get_simple_data_table endpoint'i dene
      try {
          const simpleDataUrl = this.buildURL('get_simple_data_table', {stream_id: streamId});
          console.log('📡 get_simple_data_table URL:', simpleDataUrl);
          const simpleData = await this.getJSON<any>(simpleDataUrl, 'get_simple_data_table hatası');
          console.log('📺 get_simple_data_table response:', simpleData);

          if (simpleData && simpleData.epg_listings && simpleData.epg_listings.length > 0) {
              console.log('✅ get_simple_data_table\'da veri bulundu');
              return simpleData;
          }
      } catch (error) {
          console.log('❌ get_simple_data_table hatası:', error);
      }

      console.log('❌ Hiçbir EPG endpoint\'inde veri bulunamadı');
      return {epg_listings: []};
  }

    /**
     * Kanal için detaylı EPG bilgilerini getirir (daha fazla program)
     */
    async getDetailedEPGForChannel(streamId: number): Promise<any> {
        console.log('🔍 Detaylı EPG endpoint\'leri deneniyor...');

        // 1. get_short_epg endpoint'i dene (limit parametresi ile)
        try {
            const shortEpgUrl = this.buildURL('get_short_epg', {
                stream_id: streamId,
                limit: 50 // Daha fazla program getir
            });
            console.log('📡 get_short_epg (detailed) URL:', shortEpgUrl);
            const shortEpgData = await this.getJSON<any>(shortEpgUrl, 'get_short_epg (detailed) hatası');
            console.log('📺 get_short_epg (detailed) response:', shortEpgData);

            if (shortEpgData && shortEpgData.epg_listings && shortEpgData.epg_listings.length > 0) {
                console.log('✅ get_short_epg (detailed)\'de veri bulundu');
                return shortEpgData;
            }
        } catch (error) {
            console.log('❌ get_short_epg (detailed) hatası:', error);
        }

        // 2. get_simple_data_table endpoint'i dene (limit parametresi ile)
        try {
            const simpleDataUrl = this.buildURL('get_simple_data_table', {
                stream_id: streamId,
                limit: 50
            });
            console.log('📡 get_simple_data_table (detailed) URL:', simpleDataUrl);
            const simpleData = await this.getJSON<any>(simpleDataUrl, 'get_simple_data_table (detailed) hatası');
            console.log('📺 get_simple_data_table (detailed) response:', simpleData);

            if (simpleData && simpleData.epg_listings && simpleData.epg_listings.length > 0) {
                console.log('✅ get_simple_data_table (detailed)\'da veri bulundu');
                return simpleData;
            }
        } catch (error) {
            console.log('❌ get_simple_data_table (detailed) hatası:', error);
        }

        // 3. Normal EPG endpoint'lerini dene
        return this.getEPGForChannel(streamId);
    }

    /**
     * XMLTV formatında EPG getirir
     */
    getXMLTVUrl(): string {
        return `${this.credentials.serverUrl}/xmltv.php?username=${this.credentials.username}&password=${this.credentials.password}`;
    }


  /**
   * Kanal için stream URL'sini oluşturur
   */


  /**
   * Sunucu URL'sinin geçerli olup olmadığını kontrol eder
   */
  static validateServerUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

	  /**
	   * Kullanıcıdan gelen sunucu URL'sini normalize eder
	   * - Sondaki '/' kaldırılır
	   * - '/player_api.php', '/panel_api.php', '/c', '/get.php' gibi uzantılar temizlenir
	   * - Sorgu parametreleri kaldırılır
	   */
	  static sanitizeServerUrl(input: string): string {
	    const raw = (input || '').trim();
	    try {
	      const u = new URL(raw);
	      // Bilinen son ekleri ve yolları temizle
	      const stripSuffixes = [
	        '/player_api.php',
	        '/panel_api.php',
	        '/live',
	        '/c',
	        '/get.php',
	        '/xmltv.php'
	      ];
	      let pathname = u.pathname || '';
	      for (const suf of stripSuffixes) {
	        if (pathname === suf || pathname.endsWith(suf)) {
	          pathname = pathname.slice(0, pathname.length - suf.length);
	        }
	      }
	      // Artan fazla '/' ları ve sondaki slash'i temizle
	      pathname = pathname.replace(/\/+$/, '');
	      u.pathname = pathname;
	      u.search = '';
	      u.hash = '';
	      const out = u.toString();
	      return out.replace(/\/$/, '');
	    } catch {
	      // Geçersiz URL ise sadece sondaki '/' i silip döndür
	      return raw.replace(/\/$/, '');
	    }
	  }




    /**
     * Xtream kanallarını M3U formatına dönüştürür
     * - Kategori adı boş/Undefined ise category_id -> category_name eşlemesiyle doldurur
     */
    static convertToM3UChannels(
        xtreamChannels: XtreamChannel[],
        credentials: XtreamCredentials,
        categoryMap?: Record<string, string>
    ): import('./m3u-parser').Channel[] {
        console.log('=== CONVERT TO M3U CHANNELS DEBUG ===');
        console.log('Kanal sayısı:', xtreamChannels.length);
        console.log('CategoryMap var mı?', !!categoryMap);
        console.log('CategoryMap boyutu:', categoryMap ? Object.keys(categoryMap).length : 0);
        console.log('CategoryMap içeriği:', categoryMap);

        // İlk birkaç kanalın ham verilerini kontrol et
        console.log('İlk 3 kanalın ham verileri:');
        xtreamChannels.slice(0, 3).forEach((ch, i) => {
            console.log(`Kanal ${i + 1}:`, {
                name: ch.name,
                category_name: ch.category_name,
                category_id: ch.category_id,
                category_name_type: typeof ch.category_name,
                category_id_type: typeof ch.category_id,
                full_channel: ch
            });
        });

        return xtreamChannels.map((channel, index) => {
            let groupName = 'Kategorisiz'; // Varsayılan değer

            // 1. Önce category_name'i kontrol et
            if (channel.category_name && channel.category_name.trim() !== '' && channel.category_name.toLowerCase() !== 'undefined' &&
                channel.category_name.toLowerCase() !== 'null') {
                groupName = channel.category_name.trim();
                if (index < 3) console.log(`Kanal ${index + 1}: category_name kullanıldı:`, groupName);
            }
            // 2. category_name yoksa category_id'den bul
            else if (channel.category_id && categoryMap) {
                const categoryId = String(channel.category_id);
                const mappedName = categoryMap[categoryId];
                if (mappedName && mappedName.trim() !== '') {
                    groupName = mappedName.trim();
                    if (index < 3) console.log(`Kanal ${index + 1}: category_id ${categoryId} -> ${groupName}`);
                } else {
                    if (index < 3) console.log(`Kanal ${index + 1}: category_id ${categoryId} için mapping bulunamadı`);
                }
            }
            // 3. Her ikisi de yoksa kanalın kendisinden kategori çıkarmaya çalış
            else {
                // Kanal adından kategori çıkarmaya çalış (bazı sunucular böyle yapar)
                if (channel.name && channel.name.includes(' - ')) {
                    const parts = channel.name.split(' - ');
                    if (parts.length > 1) {
                        groupName = parts[0].trim();
                        if (index < 3) console.log(`Kanal ${index + 1}: isimden kategori çıkarıldı:`, groupName);
                    }
                }
            }

            if (index < 3) {
                console.log(`Kanal ${index + 1} final grup:`, groupName);
            }

            return {
                id: `xtream_${channel.stream_id}`,
                name: channel.name,
                url: `${credentials.serverUrl}/live/${credentials.username}/${credentials.password}/${channel.stream_id}.${channel.container_extension || 'ts'}`,
                logo: channel.stream_icon,
                group: groupName,
                tvgId: channel.epg_channel_id,
                tvgName: channel.name,
                tvgLogo: channel.stream_icon,
                radioStation: false,
                duration: -1
            };
        });
    }
}
