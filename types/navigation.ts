import type {Channel} from '../utils/m3u-parser';

export type RootStackParamList = {
  Home: undefined;
  Playlists: undefined;
  Settings: undefined;
  Help: undefined;
  AddM3U: undefined;
  AddXtream: undefined;
  Channels: {
    playlistId: string;
      continuePlayback?: {
          channel: Channel;
          isPlaying: boolean;
      };
  };
  Player: {
    channel: Channel;
    playlistId: string;
    channels: Channel[];
  };
  Favorites: undefined;
  RecentlyWatched: undefined;
    VOD: {
        playlistId: string;
    };
};

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
