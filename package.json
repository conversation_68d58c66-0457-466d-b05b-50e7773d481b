{"name": "nsm-iptv-player", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.25", "expo": "53.0.22", "expo-font": "~13.3.2", "expo-keep-awake": "~14.1.4", "expo-screen-orientation": "~8.1.7", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.11", "expo-video": "~2.2.2", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}