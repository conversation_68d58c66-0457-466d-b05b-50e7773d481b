import React from 'react';
import {FlatList, Modal, Text, TextInput, TouchableOpacity, View} from 'react-native';
import {Ionicons} from '@expo/vector-icons';

interface CategoryModalProps {
    visible: boolean;
    onClose: () => void;
    filteredGroups: string[];
    categorySearchQuery: string;
    onCategorySearchChange: (query: string) => void;
    selectedCategories: Set<string>;
    onToggleCategory: (category: string) => void;
    onClearSelection: () => void;
    categoryChannelCounts: Record<string, number>;
}

export const CategoryModal = ({
                                  visible,
                                  onClose,
                                  filteredGroups,
                                  categorySearchQuery,
                                  onCategorySearchChange,
                                  selectedCategories,
                                  onToggleCategory,
                                  onClearSelection,
                                  categoryChannelCounts
                              }: CategoryModalProps) => {
    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="fade"
            onRequestClose={onClose}
            statusBarTranslucent={true}
        >
            <View style={{
                flex: 1,
                backgroundColor: 'rgba(0,0,0,0.5)',
                justifyContent: 'flex-end'
            }}>
                <View style={{
                    backgroundColor: '#1e293b',
                    borderTopLeftRadius: 24,
                    borderTopRightRadius: 24,
                    maxHeight: '70%'
                }}>
                    {/* Modal Header */}
                    <View className="flex-row items-center justify-between p-6 border-b border-slate-700">
                        <Text className="text-white text-xl font-bold">Kategoriler</Text>
                        <TouchableOpacity
                            onPress={onClose}
                            className="p-2 -mr-2"
                        >
                            <Ionicons name="close" size={24} color="white"/>
                        </TouchableOpacity>
                    </View>

                    {/* Arama */}
                    <View className="px-6 py-4 border-b border-slate-700">
                        <View className="bg-slate-700 rounded-xl flex-row items-center px-4">
                            <Ionicons name="search" size={18} color="#64748b"/>
                            <TextInput
                                className="flex-1 text-white p-3 text-base"
                                placeholder="Kategori ara..."
                                placeholderTextColor="#64748b"
                                value={categorySearchQuery}
                                onChangeText={onCategorySearchChange}
                            />
                            {categorySearchQuery.length > 0 && (
                                <TouchableOpacity onPress={() => onCategorySearchChange('')}>
                                    <Ionicons name="close-circle" size={18} color="#64748b"/>
                                </TouchableOpacity>
                            )}
                        </View>
                    </View>

                    {/* Kategori Listesi */}
                    <FlatList
                        data={filteredGroups}
                        renderItem={({item}) => (
                            <TouchableOpacity
                                className="flex-row items-center justify-between px-6 py-4 border-b border-slate-700/50"
                                onPress={() => onToggleCategory(item)}
                            >
                                <View className="flex-1">
                                    <Text className="text-white text-base font-medium">
                                        {item}
                                    </Text>
                                    <Text className="text-slate-400 text-sm mt-1">
                                        {(categoryChannelCounts[item] ?? 0)} kanal
                                    </Text>
                                </View>
                                <View className={`w-6 h-6 rounded-full border-2 items-center justify-center ${
                                    selectedCategories.has(item)
                                        ? 'bg-blue-600 border-blue-600'
                                        : 'border-slate-500'
                                }`}>
                                    {selectedCategories.has(item) && (
                                        <Ionicons name="checkmark" size={14} color="white"/>
                                    )}
                                </View>
                            </TouchableOpacity>
                        )}
                        keyExtractor={(item) => item}
                        showsVerticalScrollIndicator={false}
                        removeClippedSubviews={true}
                        maxToRenderPerBatch={10}
                        updateCellsBatchingPeriod={100}
                        initialNumToRender={20}
                        windowSize={5}
                        getItemLayout={(_, index) => ({
                            length: 72,
                            offset: 72 * index,
                            index,
                        })}
                    />

                    {/* Modal Footer */}
                    <View className="flex-row gap-3 p-6 border-t border-slate-700">
                        <TouchableOpacity
                            className="flex-1 bg-slate-600 py-4 rounded-xl"
                            onPress={onClearSelection}
                        >
                            <Text className="text-white text-center font-semibold">
                                Temizle ({selectedCategories.size})
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            className="flex-1 bg-blue-600 py-4 rounded-xl"
                            onPress={onClose}
                        >
                            <Text className="text-white text-center font-semibold">
                                Uygula
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};
