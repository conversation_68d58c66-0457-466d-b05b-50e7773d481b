import React from 'react';
import {Text, TextInput, TouchableOpacity, View} from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import type {Playlist} from '../../utils/storage';

interface ChannelHeaderProps {
    playlist: Playlist | null;
    filteredChannelsCount: number;
    searchQuery: string;
    onSearchChange: (query: string) => void;
    onGoHome: () => void;
    groups: string[];
    selectedCategories: Set<string>;
    onShowCategoryModal: () => void;
    showFavoritesOnly: boolean;
    onToggleFavoritesOnly: () => void;
}

export const ChannelHeader = ({
                                  playlist,
                                  filteredChannelsCount,
                                  searchQuery,
                                  onSearchChange,
                                  onGoHome,
                                  groups,
                                  selectedCategories,
                                  onShowCategoryModal,
                                  showFavoritesOnly,
                                  onToggleFavoritesOnly
                              }: ChannelHeaderProps) => {
    return (
        <View className="border-b border-slate-800">
            {/* Üst satır: <PERSON><PERSON><PERSON> butonu ve başlık */}
            <View className="flex-row items-center px-4 py-3">
                <TouchableOpacity
                    onPress={onGoHome}
                    className="mr-3 p-1"
                >
                    <Ionicons name="home" size={24} color="white"/>
                </TouchableOpacity>
                <View className="flex-1">
                    <Text className="text-white text-lg font-bold" numberOfLines={1}>
                        {playlist?.name}
                    </Text>
                    <Text className="text-slate-400 text-xs">
                        {showFavoritesOnly ? `${filteredChannelsCount} favori kanal` : `${filteredChannelsCount} kanal`}
                    </Text>
                </View>
                {/* Favoriler Toggle Butonu */}
                <TouchableOpacity
                    onPress={onToggleFavoritesOnly}
                    className="p-2 mr-2"
                >
                    <Ionicons
                        name={showFavoritesOnly ? "heart" : "heart-outline"}
                        size={22}
                        color={showFavoritesOnly ? "#ef4444" : "white"}
                    />
                </TouchableOpacity>


            </View>

            {/* Alt satır: Arama ve kategori filtreleme */}
            <View className="px-4 pb-3">
                <View className="flex-row items-center gap-2">
                    {/* Kanal Arama */}
                    <View className="flex-1 bg-slate-800 rounded-lg flex-row items-center px-3">
                        <Ionicons name="search" size={16} color="#64748b"/>
                        <TextInput
                            className="flex-1 text-white py-2 px-2 text-sm"
                            placeholder="Kanal ara..."
                            placeholderTextColor="#64748b"
                            value={searchQuery}
                            onChangeText={onSearchChange}
                        />
                        {searchQuery.length > 0 && (
                            <TouchableOpacity onPress={() => onSearchChange('')}>
                                <Ionicons name="close-circle" size={16} color="#64748b"/>
                            </TouchableOpacity>
                        )}
                    </View>

                    {/* Kategori Filtre Butonu - sadece favoriler modunda değilken göster */}
                    {!showFavoritesOnly && groups.length > 2 && (
                        <TouchableOpacity
                            className={`flex-row items-center px-3 py-2 rounded-lg border ${
                                selectedCategories.size > 0
                                    ? 'bg-blue-600/20 border-blue-600/50'
                                    : 'bg-slate-800 border-slate-700'
                            }`}
                            onPress={onShowCategoryModal}
                        >
                            <Ionicons
                                name="filter"
                                size={16}
                                color={selectedCategories.size > 0 ? "#3b82f6" : "#64748b"}
                            />
                            {selectedCategories.size > 0 && (
                                <View
                                    className="absolute -top-1 -right-1 bg-blue-600 rounded-full w-4 h-4 items-center justify-center">
                                    <Text className="text-white text-xs font-bold">
                                        {selectedCategories.size}
                                    </Text>
                                </View>
                            )}
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        </View>
    );
};
