import {useCallback, useState} from 'react';
import {Alert} from 'react-native';
import {type Channel, M3UParser} from '../../utils/m3u-parser';
import {XtreamAPI} from '../../utils/xtream-api';
import {ChannelStorage, FavoriteStorage, type Playlist, PlaylistStorage} from '../../utils/storage';

export const useChannelData = (playlistId: string) => {
    const [playlist, setPlaylist] = useState<Playlist | null>(null);
    const [channels, setChannels] = useState<Channel[]>([]);
    const [groups, setGroups] = useState<string[]>(['Tümü']);
    const [favorites, setFavorites] = useState<Set<string>>(new Set());
    const [isLoading, setIsLoading] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);

    const loadPlaylist = useCallback(async () => {
        try {
            const playlists = await PlaylistStorage.getPlaylists();
            const currentPlaylist = playlists.find(p => p.id === playlistId);
            if (currentPlaylist) {
                setPlaylist(currentPlaylist);
            }
        } catch (error) {
            console.error('Error loading playlist:', error);
        }
    }, [playlistId]);

    const loadChannels = useCallback(async (forceRefresh = false) => {
        if (!playlist) return;

        try {
            setIsLoading(true);
            let channelList: Channel[] = [];

            if (forceRefresh) {
                if (playlist.type === 'm3u') {
                    const m3uData = await M3UParser.fetchAndParseM3U(playlist.url);
                    channelList = m3uData.channels;
                } else if (playlist.type === 'xtream' && playlist.xtreamCredentials) {
                    const xtreamAPI = new XtreamAPI(playlist.xtreamCredentials);

                    let categories: any[] = [];
                    let categoryMap: Record<string, string> = {};

                    try {
                        categories = await xtreamAPI.getLiveCategories();
                        categories.forEach(c => {
                            if (c.category_id && c.category_name) {
                                const categoryId = String(c.category_id);
                                categoryMap[categoryId] = String(c.category_name).trim();
                            }
                        });
                    } catch (error) {
                        console.error('Error loading categories:', error);
                    }

                    const allStreams = await xtreamAPI.getLiveStreams();
                    channelList = XtreamAPI.convertToM3UChannels(allStreams, playlist.xtreamCredentials, categoryMap);

                    const kategorisizCount = channelList.filter(c => c.group === 'Kategorisiz').length;
                    if (kategorisizCount === channelList.length && channelList.length > 0) {
                        channelList = channelList.map(channel => {
                            let newGroup = 'Genel';
                            const name = channel.name.toLowerCase();

                            if (name.includes('haber') || name.includes('news')) {
                                newGroup = 'Haber';
                            } else if (name.includes('spor') || name.includes('sport')) {
                                newGroup = 'Spor';
                            } else if (name.includes('film') || name.includes('movie') || name.includes('sinema')) {
                                newGroup = 'Film';
                            } else if (name.includes('müzik') || name.includes('music')) {
                                newGroup = 'Müzik';
                            } else if (name.includes('çocuk') || name.includes('kids') || name.includes('cartoon')) {
                                newGroup = 'Çocuk';
                            } else if (name.includes('belgesel') || name.includes('documentary')) {
                                newGroup = 'Belgesel';
                            } else if (name.includes('dizi') || name.includes('series')) {
                                newGroup = 'Dizi';
                            }

                            return {...channel, group: newGroup};
                        });
                    }
                }

                await ChannelStorage.saveChannels(playlist.id, channelList);
                await PlaylistStorage.updatePlaylistChannelCount(playlist.id, channelList.length);
            } else {
                channelList = await ChannelStorage.getChannels(playlist.id);

                const isXtreamWithoutGroups = playlist.type === 'xtream' &&
                    channelList.length > 0 &&
                    channelList.every(c => !c.group || c.group === 'Kategorisiz');

                if (channelList.length === 0 || isXtreamWithoutGroups) {
                    if (playlist.type === 'm3u') {
                        const m3uData = await M3UParser.fetchAndParseM3U(playlist.url);
                        channelList = m3uData.channels;
                    } else if (playlist.type === 'xtream' && playlist.xtreamCredentials) {
                        const xtreamAPI = new XtreamAPI(playlist.xtreamCredentials);

                        let categories: any[] = [];
                        let categoryMap: Record<string, string> = {};

                        try {
                            categories = await xtreamAPI.getLiveCategories();
                            categories.forEach(c => {
                                if (c.category_id && c.category_name) {
                                    const categoryId = String(c.category_id);
                                    categoryMap[categoryId] = String(c.category_name).trim();
                                }
                            });
                        } catch (error) {
                            console.error('Error loading categories:', error);
                        }

                        const allStreams = await xtreamAPI.getLiveStreams();
                        channelList = XtreamAPI.convertToM3UChannels(allStreams, playlist.xtreamCredentials, categoryMap);
                    }

                    await ChannelStorage.saveChannels(playlist.id, channelList);
                    await PlaylistStorage.updatePlaylistChannelCount(playlist.id, channelList.length);
                }

                if (playlist.type === 'xtream' && channelList.length > 0) {
                    const hasGroups = channelList.some(c => c.group && c.group !== 'Kategorisiz');
                    if (!hasGroups) {
                        channelList = channelList.map(channel => {
                            let newGroup = 'Genel';
                            const name = channel.name.toLowerCase();

                            if (name.includes('haber') || name.includes('news')) {
                                newGroup = 'Haber';
                            } else if (name.includes('spor') || name.includes('sport')) {
                                newGroup = 'Spor';
                            } else if (name.includes('film') || name.includes('movie') || name.includes('sinema')) {
                                newGroup = 'Film';
                            } else if (name.includes('müzik') || name.includes('music')) {
                                newGroup = 'Müzik';
                            } else if (name.includes('çocuk') || name.includes('kids') || name.includes('cartoon')) {
                                newGroup = 'Çocuk';
                            } else if (name.includes('belgesel') || name.includes('documentary')) {
                                newGroup = 'Belgesel';
                            } else if (name.includes('dizi') || name.includes('series')) {
                                newGroup = 'Dizi';
                            }

                            return {...channel, group: newGroup};
                        });

                        await ChannelStorage.saveChannels(playlist.id, channelList);
                    }
                }
            }

            setChannels(channelList);
            const channelGroups = [...new Set(channelList.map(ch => ch.group).filter((group): group is string => Boolean(group)))].sort();
            setGroups(['Tümü', ...channelGroups]);

        } catch (error) {
            Alert.alert('Hata', 'Kanallar yüklenirken bir hata oluştu.');
        } finally {
            setIsLoading(false);
            setIsRefreshing(false);
        }
    }, [playlist]);

    const loadFavorites = useCallback(async () => {
        try {
            const favoriteChannels = await FavoriteStorage.getFavorites();
            const favoriteIds = new Set(favoriteChannels.map(fav => fav.channelId));
            setFavorites(favoriteIds);
        } catch (error) {
            console.error('Error loading favorites:', error);
        }
    }, []);

    const toggleFavorite = async (channel: Channel) => {
        try {
            const isFav = favorites.has(channel.id);

            if (isFav) {
                await FavoriteStorage.removeFavorite(channel.id);
                setFavorites(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(channel.id);
                    return newSet;
                });
            } else {
                await FavoriteStorage.addFavorite(channel, playlistId);
                setFavorites(prev => new Set(prev).add(channel.id));
            }
        } catch (error) {
            Alert.alert('Hata', 'Favori işlemi sırasında bir hata oluştu.');
        }
    };

    const handleRefresh = () => {
        setIsRefreshing(true);
        void loadChannels(true);
    };

    return {
        playlist,
        channels,
        groups,
        favorites,
        isLoading,
        isRefreshing,
        loadPlaylist,
        loadChannels,
        loadFavorites,
        toggleFavorite,
        handleRefresh
    };
};
