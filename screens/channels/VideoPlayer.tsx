import React from 'react';
import {View} from 'react-native';
import {VideoPlayer as ExpoVideoPlayer, VideoView} from 'expo-video';
import type {Channel} from '../../utils/m3u-parser';

interface VideoPlayerProps {
    selectedChannel: Channel | null;
    player: ExpoVideoPlayer | null;
}

export const VideoPlayer = ({selectedChannel, player}: VideoPlayerProps) => {
    if (!selectedChannel || !player) return null;
    return (
        <View style={{
            height: 220,
            backgroundColor: '#0f172a',
            paddingHorizontal: 16,
            paddingVertical: 8,
            zIndex: 10,
            elevation: 10,
            position: 'relative'
        }}>
            <View style={{
                flex: 1,
                backgroundColor: '#000',
                borderRadius: 12,
                overflow: 'hidden',
                position: 'relative'
            }}>
                <VideoView
                    style={{
                        flex: 1,
                        width: '100%',
                        height: '100%'
                    }}
                    player={player}
                    allowsFullscreen={true}
                    allowsPictureInPicture={true}
                    allowsVideoFrameAnalysis={true}
                    contentFit="contain"
                    nativeControls={true}
                    showsTimecodes={true}
                    requiresLinearPlayback={false}
                    startsPictureInPictureAutomatically={false}
                />
            </View>
        </View>
    );
};
