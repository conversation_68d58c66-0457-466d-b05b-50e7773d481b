import {useCallback, useEffect, useMemo, useState} from 'react';
import {type Channel} from '../../utils/m3u-parser';
import {CategoryFilterStorage} from '../../utils/storage';

// Header tipi tanımı
interface HeaderItem {
    type: 'header';
    title: string;
    count: number;
}

// Channel veya Header item tipi
type ChannelListItem = Channel | HeaderItem;

// Type guard fonksiyonları
export const isHeaderItem = (item: ChannelListItem): item is HeaderItem => {
    return 'type' in item && item.type === 'header';
};

export const isChannelItem = (item: ChannelListItem): item is Channel => {
    return !('type' in item) || (item as any).type !== 'header';
};

export const useCategoryFiltering = (channels: Channel[], groups: string[], playlistId: string, favorites: Set<string>, showFavoritesOnly: boolean) => {
    const [selectedGroup, setSelectedGroup] = useState('Tümü');
    const [searchQuery, setSearchQuery] = useState('');
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
    const [categorySearchQuery, setCategorySearchQuery] = useState('');
    const [selectedCategories, setSelectedCategories] = useState<Set<string>>(new Set());
    const [showCategoryModal, setShowCategoryModal] = useState(false);

    // Debounced search
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchQuery(searchQuery);
        }, 300);
        return () => clearTimeout(timer);
    }, [searchQuery]);

    // Load category filters
    const loadCategoryFilters = useCallback(async () => {
        try {
            const filters = await CategoryFilterStorage.getCategoryFilters(playlistId);
            if (filters) {
                setSelectedCategories(new Set(filters.selectedCategories));
                setSelectedGroup(filters.selectedGroup);
            }
        } catch (error) {
            console.error('Kategori filtreleri yüklenirken hata:', error);
        }
    }, [playlistId]);

    // Save category filters when selectedGroup changes
    useEffect(() => {
        if (selectedGroup !== 'Tümü') {
            const saveCategoryFilters = async () => {
                try {
                    await CategoryFilterStorage.saveCategoryFilters(
                        playlistId,
                        Array.from(selectedCategories),
                        selectedGroup
                    );
                } catch (error) {
                    console.error('Kategori filtreleri kaydedilirken hata:', error);
                }
            };
            void saveCategoryFilters();
        }
    }, [selectedGroup, selectedCategories, playlistId]);

    const toggleCategory = useCallback(async (category: string) => {
        const newSelected = new Set(selectedCategories);
        if (newSelected.has(category)) {
            newSelected.delete(category);
        } else {
            newSelected.add(category);
        }
        setSelectedCategories(newSelected);
        setSelectedGroup('Tümü');

        try {
            await CategoryFilterStorage.saveCategoryFilters(
                playlistId,
                Array.from(newSelected),
                'Tümü'
            );
        } catch (error) {
            console.error('Kategori filtreleri kaydedilirken hata:', error);
        }
    }, [selectedCategories, playlistId]);

    const clearCategorySelection = useCallback(async () => {
        setSelectedCategories(new Set());
        setSelectedGroup('Tümü');

        try {
            await CategoryFilterStorage.saveCategoryFilters(playlistId, [], 'Tümü');
        } catch (error) {
            console.error('Kategori filtreleri temizlenirken hata:', error);
        }
    }, [playlistId]);

    // Filtered channels with headers
    const filteredChannelsWithHeaders = useMemo(() => {
        let filtered = channels;

        // Önce favori filtresi uygula
        if (showFavoritesOnly) {
            filtered = filtered.filter(channel => favorites.has(channel.id));
        }

        if (selectedGroup !== 'Tümü') {
            filtered = filtered.filter(channel => channel.group === selectedGroup);
        }

        if (selectedCategories.size > 0) {
            filtered = filtered.filter(channel =>
                channel.group && selectedCategories.has(channel.group)
            );
        }

        if (debouncedSearchQuery.trim()) {
            const searchTerm = debouncedSearchQuery.toLowerCase().trim();
            filtered = filtered.filter(channel =>
                channel.name.toLowerCase().includes(searchTerm) ||
                (channel.group && channel.group.toLowerCase().includes(searchTerm))
            );
        }

        // Favori modunda header'ları gösterme
        if (showFavoritesOnly) {
            return filtered;
        }

        if (selectedGroup === 'Tümü' && selectedCategories.size === 0 && !debouncedSearchQuery.trim()) {
            const grouped: { [key: string]: Channel[] } = {};
            filtered.forEach(channel => {
                const group = channel.group || 'Diğer';
                if (!grouped[group]) {
                    grouped[group] = [];
                }
                grouped[group].push(channel);
            });

            const result: (Channel | { type: 'header', title: string, count: number })[] = [];
            Object.keys(grouped)
                .sort((a, b) => {
                    if (a === 'Diğer') return 1;
                    if (b === 'Diğer') return -1;
                    return a.localeCompare(b, 'tr');
                })
                .forEach(groupName => {
                    result.push({
                        type: 'header',
                        title: groupName,
                        count: grouped[groupName].length
                    });
                    result.push(...grouped[groupName]);
                });
            return result;
        }

        return filtered;
    }, [channels, selectedGroup, selectedCategories, debouncedSearchQuery, showFavoritesOnly, favorites]);

    const filteredChannels = useMemo(() => {
        return Array.isArray(filteredChannelsWithHeaders)
            ? filteredChannelsWithHeaders.filter(isChannelItem)
            : filteredChannelsWithHeaders as Channel[];
    }, [filteredChannelsWithHeaders]);

    const filteredGroups = useMemo(() => {
        if (!categorySearchQuery.trim()) {
            return groups.filter(group => group !== 'Tümü');
        }
        const searchTerm = categorySearchQuery.toLowerCase();
        return groups.filter(group =>
            group !== 'Tümü' && group.toLowerCase().includes(searchTerm)
        );
    }, [groups, categorySearchQuery]);

    const categoryChannelCounts = useMemo(() => {
        const counts: Record<string, number> = {};
        channels.forEach(channel => {
            const group = channel.group || 'Diğer';
            counts[group] = (counts[group] || 0) + 1;
        });
        return counts;
    }, [channels]);

    return {
        selectedGroup,
        setSelectedGroup,
        searchQuery,
        setSearchQuery,
        debouncedSearchQuery,
        categorySearchQuery,
        setCategorySearchQuery,
        selectedCategories,
        showCategoryModal,
        setShowCategoryModal,
        filteredChannelsWithHeaders,
        filteredChannels,
        filteredGroups,
        categoryChannelCounts,
        loadCategoryFilters,
        toggleCategory,
        clearCategorySelection
    };
};
