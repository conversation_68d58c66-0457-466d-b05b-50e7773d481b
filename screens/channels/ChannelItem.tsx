import React, {memo} from 'react';
import {Image, Text, TouchableOpacity, View} from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import type {Channel} from '../../utils/m3u-parser';
import type {UserPreferences} from '../../utils/storage';

interface ChannelItemProps {
    item: Channel;
    onPlay: () => void;
    onToggleFavorite: () => void;
    onLoadEPG?: () => void;
    onShowEPGDetails?: () => void;
    isFavorite: boolean;
    isSelected?: boolean;
    preferences: UserPreferences;
    epgData?: string;
    isLoadingEPG?: boolean;
}

export const ChannelItem = memo(({
                                     item,
                                     onPlay,
                                     onToggleFavorite,
                                     onLoadEPG,
                                     onShowEPGDetails,
                                     isFavorite,
                                     isSelected = false,
                                     preferences,
                                     epgData,
                                     isLoadingEPG = false
                                 }: ChannelItemProps) => (
    <TouchableOpacity
        style={{
            backgroundColor: isSelected ? '#0f172a' : '#1e293b',
            borderRadius: 12,
            padding: 12,
            marginBottom: 8,
            flexDirection: 'row',
            alignItems: 'center',
            height: 80,
            borderWidth: isSelected ? 1 : 0,
            borderColor: isSelected ? '#334155' : 'transparent',
        }}
        onPress={onPlay}
        activeOpacity={0.7}
    >
        {/* Logo alanı - preferences'a göre göster/gizle */}
        {preferences.showChannelLogos && (
            <View style={{
                width: 56,
                height: 56,
                backgroundColor: '#334155',
                borderRadius: 8,
                marginRight: 12,
                overflow: 'hidden',
                justifyContent: 'center',
                alignItems: 'center'
            }}>
                {item.logo ? (
                    <Image
                        source={{uri: item.logo}}
                        style={{width: 56, height: 56}}
                        resizeMode="cover"
                    />
                ) : (
                    <Ionicons name="tv-outline" size={24} color="#64748b"/>
                )}
            </View>
        )}

        <View style={{flex: 1, justifyContent: 'center'}}>
            <Text
                style={{
                    color: 'white',
                    fontSize: 16,
                    fontWeight: '600',
                    marginBottom: 4
                }}
                numberOfLines={1}
            >
                {item.name}
            </Text>
            {/* Grup bilgisi */}
            {item.group && (
                <Text
                    style={{
                        color: '#94a3b8',
                        fontSize: 14
                    }}
                    numberOfLines={1}
                >
                    {item.group}
                </Text>
            )}

            {/* EPG bilgisi - sadece yüklendiyse göster */}
            {epgData && (
                <Text
                    style={{
                        color: '#64748b',
                        fontSize: 12,
                        marginTop: 2
                    }}
                    numberOfLines={1}
                >
                    📺 {epgData}
                </Text>
            )}
        </View>

        {/* Sağ taraf butonları - EPG ve Favori yan yana, sağa yakın */}
        <View style={{flexDirection: 'row', alignItems: 'center', marginLeft: 20}}>
            {/* EPG butonu - direkt modal aç */}
            {preferences.showEPG && onShowEPGDetails && (
                <TouchableOpacity
                    style={{
                        padding: 6,
                        marginRight: 4,
                        width: 36,
                        height: 36,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}
                    onPress={onShowEPGDetails}
                    hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
                >
                    <Ionicons
                        name="calendar"
                        size={18}
                        color="#64748b"
                    />
                </TouchableOpacity>
            )}

            {/* Favori butonu */}
            <TouchableOpacity
                style={{
                    padding: 6,
                    width: 36,
                    height: 36,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}
                onPress={onToggleFavorite}
                hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
            >
                <Ionicons
                    name={isFavorite ? "heart" : "heart-outline"}
                    size={20}
                    color={isFavorite ? "#ef4444" : "#64748b"}
                />
            </TouchableOpacity>
        </View>
    </TouchableOpacity>
));

ChannelItem.displayName = 'ChannelItem';
