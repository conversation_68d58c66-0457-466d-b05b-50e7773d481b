import React, {memo} from 'react';
import {Text, View} from 'react-native';

interface CategoryHeaderProps {
    title: string;
    count: number;
}

export const CategoryHeader = memo(({title, count}: CategoryHeaderProps) => (
    <View style={{
        backgroundColor: '#0f172a',
        paddingHorizontal: 16,
        paddingVertical: 12,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 50,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(51, 65, 85, 0.3)',
    }}>
        <Text style={{
            color: 'white',
            fontSize: 18,
            fontWeight: 'bold',
            flex: 1,
        }} numberOfLines={1}>
            {title}
        </Text>
        <View style={{
            backgroundColor: 'rgba(51, 65, 85, 0.5)',
            paddingHorizontal: 8,
            paddingVertical: 2,
            borderRadius: 12,
        }}>
            <Text style={{
                color: '#cbd5e1',
                fontSize: 12,
                fontWeight: '500',
            }}>
                {count}
            </Text>
        </View>
    </View>
));

CategoryHeader.displayName = 'CategoryHeader';
