import React, {useCallback, useEffect, useRef, useState} from 'react';
import {ActivityIndicator, FlatList, StatusBar, Text, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Ionicons} from '@expo/vector-icons';
import type {RouteProp} from '@react-navigation/native';
import {useFocusEffect, useNavigation, useRoute} from '@react-navigation/native';
import * as ScreenOrientation from 'expo-screen-orientation';
import type {RootStackParamList} from '../../types/navigation';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CategoryHeader} from './CategoryHeader';
import {ChannelItem} from './ChannelItem';
import {CategoryModal} from './CategoryModal';
import {EPGModal} from './EPGModal';
import {VideoPlayer as VideoPlayerComponent} from './VideoPlayer';
import {ChannelHeader} from './ChannelHeader';
import {useChannelData} from './useChannelData';
import {isHeaderItem, useCategoryFiltering} from './useCategoryFiltering';
import {useVideoPlayerLogic} from './useVideoPlayer';
import {PreferencesStorage, type UserPreferences} from '../../utils/storage';
import type {Channel} from '../../utils/m3u-parser';
import {XtreamAPI} from '../../utils/xtream-api';

type ChannelsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Channels'>;
type ChannelsScreenRouteProp = RouteProp<RootStackParamList, 'Channels'>;

const ChannelsScreen = () => {
    const navigation = useNavigation<ChannelsScreenNavigationProp>();
    const route = useRoute<ChannelsScreenRouteProp>();
    const {playlistId, continuePlayback} = route.params;

    // FlatList ref for scrolling
    const flatListRef = useRef<FlatList>(null);

    // Preferences state
    const [preferences, setPreferences] = useState<UserPreferences>(PreferencesStorage.getDefaultPreferences());

    // EPG state - her kanal için ayrı EPG verisi
    const [epgData, setEpgData] = useState<Map<string, string>>(new Map());
    const [loadingEPG, setLoadingEPG] = useState<Set<string>>(new Set());

    // EPG Modal state
    const [showEPGModal, setShowEPGModal] = useState(false);
    const [selectedEPGChannel, setSelectedEPGChannel] = useState<Channel | null>(null);

    // Favorites only mode state
    const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

    // Portrait modunu zorla
    useEffect(() => {
        const lockOrientation = async () => {
            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
                console.error('Orientation lock failed:', error);
            }
        };
        void lockOrientation();
    }, []);

    // Custom hooks
    const {
        playlist,
        channels,
        groups,
        favorites,
        isLoading,
        isRefreshing,
        loadPlaylist,
        loadChannels,
        loadFavorites,
        toggleFavorite,
        handleRefresh
    } = useChannelData(playlistId);

    const {
        filteredChannelsWithHeaders,
        filteredChannels,
        filteredGroups,
        categoryChannelCounts,
        searchQuery,
        setSearchQuery,
        debouncedSearchQuery,
        categorySearchQuery,
        setCategorySearchQuery,
        selectedCategories,
        showCategoryModal,
        setShowCategoryModal,
        loadCategoryFilters,
        toggleCategory,
        clearCategorySelection
    } = useCategoryFiltering(channels, groups, playlistId, favorites, showFavoritesOnly);

    const {
        selectedChannel,
        player,
        playChannel,
        closePlayer,
    } = useVideoPlayerLogic(channels, playlistId);

    // Toggle favorites only mode - hook'lardan sonra tanımla
    const toggleFavoritesOnly = useCallback(() => {
        setShowFavoritesOnly(prev => {
            const newValue = !prev;
            // Favori moduna geçerken kategori filtrelerini temizle
            if (newValue) {
                clearCategorySelection();
            }
            return newValue;
        });
    }, [clearCategorySelection]);

    // Player screen'den geri dönüldüğünde video oynatmayı devam ettir
    useEffect(() => {
        if (continuePlayback && continuePlayback.channel && channels.length > 0) {
            // Önce mevcut player'ı tamamen temizle
            if (selectedChannel) {
                closePlayer();
            }

            // Kısa bir gecikme ile yeni kanalı oynat
            setTimeout(() => {
                const channelToPlay = channels.find(c => c.id === continuePlayback.channel.id);
                if (channelToPlay) {
                    playChannel(channelToPlay);

                    // Navigation parametrelerini temizle
                    navigation.setParams({
                        playlistId,
                        continuePlayback: undefined
                    });
                }
            }, 500); // 500ms gecikme ile eski player'ın tamamen temizlenmesini bekle
        }
    }, [continuePlayback, channels, playChannel, navigation, playlistId, selectedChannel, closePlayer]);

    // Seçili kanal değiştiğinde scroll et


    // FlatList optimizasyonları
    const ITEM_HEIGHT = 88;
    const HEADER_HEIGHT = 50;

    const getItemLayout = useCallback((data: any, index: number) => {
        let offset = 0;
        let currentHeight = ITEM_HEIGHT;

        for (let i = 0; i < index; i++) {
            const item = data[i];
            if (item && typeof item === 'object' && 'type' in item && item.type === 'header') {
                offset += HEADER_HEIGHT;
            } else {
                offset += ITEM_HEIGHT;
            }
        }

        const currentItem = data[index];
        if (currentItem && typeof currentItem === 'object' && 'type' in currentItem && currentItem.type === 'header') {
            currentHeight = HEADER_HEIGHT;
        }

        return {
            length: currentHeight,
            offset,
            index,
        };
    }, []);


    // Preferences'ları yükle
    useEffect(() => {
        const loadPreferences = async () => {
            try {
                const prefs = await PreferencesStorage.getPreferences();
                setPreferences(prefs);
            } catch (error) {
                console.error('Error loading preferences:', error);
            }
        };
        loadPreferences();
    }, []);

    // Effects
    useFocusEffect(
        useCallback(() => {
            void loadPlaylist();
            void loadFavorites();
        }, [loadPlaylist, loadFavorites])
    );

    useEffect(() => {
        if (playlist) {
            void loadChannels();
            void loadCategoryFilters();
        }
    }, [playlist, loadChannels, loadCategoryFilters]);

    // EPG yükleme fonksiyonu
    const loadEPGForChannel = useCallback(async (channel: Channel) => {
        if (loadingEPG.has(channel.id)) return; // Zaten yükleniyor

        console.log('🔍 EPG yükleme başladı:', {
            channelId: channel.id,
            channelName: channel.name,
            playlistType: playlist?.type
        });

        setLoadingEPG(prev => new Set([...prev, channel.id]));

        try {
            let epgText = '';

            // Xtream Codes playlist'i ise gerçek EPG verisi çek
            if (playlist?.type === 'xtream' && playlist.xtreamCredentials) {
                console.log('📡 Xtream API\'den EPG çekiliyor...');
                const xtreamAPI = new XtreamAPI(playlist.xtreamCredentials);

                // Kanal ID'sini Xtream stream_id'sine çevir
                const streamId = channel.id.replace('xtream_', '');
                console.log('🆔 Stream ID:', streamId);

                const epgData = await xtreamAPI.getEPGForChannel(parseInt(streamId));
                console.log('📺 EPG API Response:', epgData);

                if (epgData && epgData.epg_listings && epgData.epg_listings.length > 0) {
                    console.log('✅ EPG verisi bulundu:', epgData.epg_listings[0]);
                    const currentProgram = epgData.epg_listings[0];
                    const startTime = new Date(currentProgram.start_timestamp * 1000);
                    const endTime = new Date(currentProgram.stop_timestamp * 1000);

                    // Program başlığını temizle
                    const cleanTitle = (currentProgram.title || '')
                        .replace(/&amp;/g, '&')
                        .replace(/&lt;/g, '<')
                        .replace(/&gt;/g, '>')
                        .replace(/&quot;/g, '"')
                        .replace(/&#39;/g, "'")
                        .replace(/&nbsp;/g, ' ')
                        .replace(/[^\w\s\u00C0-\u017F\u0100-\u024F\u1E00-\u1EFF.,!?:;()\-'"]/g, '')
                        .trim() || 'Bilinmeyen Program';

                    epgText = `${startTime.toLocaleTimeString('tr-TR', {
                        hour: '2-digit',
                        minute: '2-digit'
                    })}-${endTime.toLocaleTimeString('tr-TR', {
                        hour: '2-digit',
                        minute: '2-digit'
                    })} ${cleanTitle}`;

                    console.log('📝 Formatlanmış EPG:', epgText);
                } else {
                    console.log('❌ EPG verisi bulunamadı');
                    epgText = 'EPG verisi bulunamadı';
                }
            } else {
                console.log('📻 M3U playlist - varsayılan mesaj');
                // M3U playlist'i için varsayılan mesaj
                epgText = `Canlı Yayın - ${new Date().toLocaleTimeString('tr-TR', {
                    hour: '2-digit',
                    minute: '2-digit'
                })}`;
            }

            console.log('💾 EPG verisi kaydediliyor:', epgText);
            setEpgData(prev => new Map([...prev, [channel.id, epgText]]));
        } catch (error) {
            console.error('❌ EPG yükleme hatası:', error);
            setEpgData(prev => new Map([...prev, [channel.id, 'EPG yüklenemedi']]));
        } finally {
            setLoadingEPG(prev => {
                const newSet = new Set(prev);
                newSet.delete(channel.id);
                return newSet;
            });
        }
    }, [loadingEPG, playlist]);

    // EPG detaylarını göster - direkt modal aç
    const showEPGDetails = useCallback((channel: Channel) => {
        setSelectedEPGChannel(channel);
        setShowEPGModal(true);
    }, []);

    const renderItem = useCallback(({ item }: { item: any }) => {
        if (item && typeof item === 'object' && 'type' in item && item.type === 'header') {
            return <CategoryHeader title={item.title} count={item.count} />;
        }

        return (
            <ChannelItem
                item={item}
                onPlay={() => playChannel(item)}
                onToggleFavorite={() => toggleFavorite(item)}
                onLoadEPG={() => showEPGDetails(item)}
                onShowEPGDetails={() => showEPGDetails(item)}
                isFavorite={favorites.has(item.id)}
                isSelected={selectedChannel?.id === item.id}
                preferences={preferences}
                epgData={epgData.get(item.id)}
                isLoadingEPG={loadingEPG.has(item.id)}
            />
        );
    }, [favorites, playChannel, toggleFavorite, selectedChannel, preferences, epgData, loadingEPG, showEPGDetails]);

    // Seçili kanala scroll et
    const scrollToSelectedChannel = useCallback(() => {
        if (selectedChannel && flatListRef.current && filteredChannelsWithHeaders.length > 0) {
            const index = filteredChannelsWithHeaders.findIndex(item =>
                item && typeof item === 'object' && 'id' in item && item.id === selectedChannel.id
            );

            if (index >= 0) {
                setTimeout(() => {
                    flatListRef.current?.scrollToIndex({
                        index,
                        animated: true,
                        viewPosition: 0.5, // Ortada göster
                    });
                }, 100);
            }
        }
    }, [selectedChannel, filteredChannelsWithHeaders]);

    // Seçili kanala scroll et
    useEffect(() => {
        if (selectedChannel) {
            scrollToSelectedChannel();
        }
    }, [selectedChannel, scrollToSelectedChannel]);

    if (isLoading && !isRefreshing) {
        return (
            <SafeAreaView className="flex-1 bg-slate-900">
                <StatusBar
                    barStyle="light-content"
                    backgroundColor="#0f172a"
                    hidden={false}
                    animated={true}
                />
                <View className="flex-1 items-center justify-center">
                    <ActivityIndicator size="large" color="#3b82f6" />
                    <Text className="text-white text-lg mt-4">Kanallar yükleniyor...</Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <>
            <CategoryModal
                visible={showCategoryModal}
                onClose={() => setShowCategoryModal(false)}
                filteredGroups={filteredGroups}
                categorySearchQuery={categorySearchQuery}
                onCategorySearchChange={setCategorySearchQuery}
                selectedCategories={selectedCategories}
                onToggleCategory={toggleCategory}
                onClearSelection={clearCategorySelection}
                categoryChannelCounts={categoryChannelCounts}
            />

            <EPGModal
                visible={showEPGModal}
                onClose={() => setShowEPGModal(false)}
                channel={selectedEPGChannel}
                playlist={playlist}
            />

            <SafeAreaView className="flex-1 bg-slate-900">
                <StatusBar
                    barStyle="light-content"
                    backgroundColor="#0f172a"
                    hidden={false}
                    animated={true}
                />

                <View className="flex-1" style={{position: 'relative'}}>
                <ChannelHeader
                    playlist={playlist}
                    filteredChannelsCount={filteredChannels.length}
                    searchQuery={searchQuery}
                    onSearchChange={setSearchQuery}
                    onGoHome={() => navigation.navigate('Home')}
                    groups={groups}
                    selectedCategories={selectedCategories}
                    onShowCategoryModal={() => setShowCategoryModal(true)}
                    showFavoritesOnly={showFavoritesOnly}
                    onToggleFavoritesOnly={toggleFavoritesOnly}
                />

                    {/* Video Player - Kanal listesi üzerinde */}
                    {selectedChannel && (
                    <View style={{zIndex: 10, elevation: 10}}>
                        <VideoPlayerComponent
                            selectedChannel={selectedChannel}
                            player={player}
                        />
                    </View>
                )}

                {/* Kanal listesi - Ultra optimize edilmiş FlatList */}
                <FlatList
                    ref={flatListRef}
                    data={filteredChannelsWithHeaders}
                    renderItem={renderItem}
                    keyExtractor={(item, index) => {
                        if (isHeaderItem(item)) {
                            return `header-${item.title}`;
                        }
                        return item.id || `item-${index}`;
                    }}
                    style={{flex: 1}}
                    contentContainerStyle={{paddingHorizontal: 16, paddingVertical: 8}}
                    showsVerticalScrollIndicator={false}
                    removeClippedSubviews={true}
                    maxToRenderPerBatch={8}
                    updateCellsBatchingPeriod={100}
                    initialNumToRender={15}
                    windowSize={5}
                    getItemLayout={getItemLayout}
                    scrollEventThrottle={16}
                    onScrollToIndexFailed={(info) => {
                        // Scroll hatası durumunda normal scroll yap
                        setTimeout(() => {
                            flatListRef.current?.scrollToOffset({
                                offset: info.averageItemLength * info.index,
                                animated: true,
                            });
                        }, 100);
                    }}

                    ListEmptyComponent={
                        <View style={{
                            flex: 1,
                            alignItems: 'center',
                            justifyContent: 'center',
                            paddingVertical: 80
                        }}>
                            <Ionicons name="tv-outline" size={64} color="#64748b"/>
                            <Text style={{
                                color: '#94a3b8',
                                fontSize: 18,
                                marginTop: 16,
                                textAlign: 'center'
                            }}>
                                {debouncedSearchQuery || selectedCategories.size > 0 ? 'Sonuç bulunamadı' : 'Kanal bulunamadı'}
                            </Text>
                        </View>
                    }
                />
            </View>


            </SafeAreaView>


        </>
    );
};

export default ChannelsScreen;
