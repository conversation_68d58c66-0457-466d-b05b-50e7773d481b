import React, {useEffect, useState} from 'react';
import {ActivityIndicator, Modal, RefreshControl, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import type {Channel} from '../../utils/m3u-parser';
import type {Playlist} from '../../utils/storage';
import {XtreamAPI} from '../../utils/xtream-api';

interface EPGProgram {
    id: string;
    title: string;
    description?: string;
    start_timestamp: number;
    stop_timestamp: number;
    category?: string;
    lang?: string;
}

interface EPGModalProps {
    visible: boolean;
    onClose: () => void;
    channel: Channel | null;
    playlist: Playlist | null;
}

export const EPGModal = ({visible, onClose, channel, playlist}: EPGModalProps) => {
    const [epgPrograms, setEpgPrograms] = useState<EPGProgram[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const loadEPGData = async () => {
        if (!channel || !playlist) return;

        setLoading(true);
        setError(null);

        try {
            if (playlist.type === 'xtream' && playlist.xtreamCredentials) {
                const xtreamAPI = new XtreamAPI(playlist.xtreamCredentials);
                const streamId = channel.id.replace('xtream_', '');

                console.log('🔍 EPG detayları yükleniyor:', {
                    channelName: channel.name,
                    streamId: streamId
                });

                const epgData = await xtreamAPI.getDetailedEPGForChannel(parseInt(streamId));

                console.log('🔍 Ham EPG verisi:', JSON.stringify(epgData, null, 2));

                if (epgData && epgData.epg_listings && epgData.epg_listings.length > 0) {
                    console.log('✅ EPG detayları bulundu:', epgData.epg_listings);

                    // Her program için detaylı log
                    epgData.epg_listings.forEach((program: any, index: number) => {
                        console.log(`📺 Program ${index + 1}:`, {
                            title: program.title,
                            description: program.description,
                            start_timestamp: program.start_timestamp,
                            stop_timestamp: program.stop_timestamp,
                            startTime: new Date(program.start_timestamp * 1000).toLocaleString('tr-TR'),
                            endTime: new Date(program.stop_timestamp * 1000).toLocaleString('tr-TR'),
                            rawProgram: program
                        });
                    });

                    setEpgPrograms(epgData.epg_listings);
                } else {
                    setError('Bu kanal için EPG verisi bulunamadı');
                }
            } else {
                setError('M3U playlist\'leri için EPG detayları desteklenmiyor');
            }
        } catch (err) {
            console.error('❌ EPG detay yükleme hatası:', err);
            setError('EPG verileri yüklenirken hata oluştu');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (visible && channel) {
            loadEPGData();
        }
    }, [visible, channel]);

    const formatTime = (timestamp: number) => {
        return new Date(timestamp * 1000).toLocaleTimeString('tr-TR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const formatDate = (timestamp: number) => {
        return new Date(timestamp * 1000).toLocaleDateString('tr-TR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatDuration = (startTimestamp: number, endTimestamp: number) => {
        const durationMs = (endTimestamp - startTimestamp) * 1000;
        const minutes = Math.floor(durationMs / (1000 * 60));
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;

        if (hours > 0) {
            return `${hours}s ${remainingMinutes}dk`;
        }
        return `${remainingMinutes}dk`;
    };

    const isCurrentProgram = (startTimestamp: number, endTimestamp: number) => {
        const now = Date.now() / 1000;
        return startTimestamp <= now && endTimestamp > now;
    };

    const cleanText = (text: string) => {
        if (!text) return '';

        // HTML entity'leri decode et
        return text
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/&nbsp;/g, ' ')
            // Garip karakterleri temizle
            .replace(/[^\w\s\u00C0-\u017F\u0100-\u024F\u1E00-\u1EFF.,!?:;()\-'"]/g, '')
            .trim();
    };

    const renderProgram = (program: EPGProgram, index: number) => {
        const isCurrent = isCurrentProgram(program.start_timestamp, program.stop_timestamp);
        const startTime = formatTime(program.start_timestamp);
        const endTime = formatTime(program.stop_timestamp);
        const duration = formatDuration(program.start_timestamp, program.stop_timestamp);

        // Program başlığı ve açıklamasını temizle
        const cleanTitle = cleanText(program.title) || 'Bilinmeyen Program';
        const cleanDescription = program.description ? cleanText(program.description) : null;

        return (
            <View
                key={program.id || index}
                style={{
                    backgroundColor: isCurrent ? '#1e40af' : '#374151',
                    borderRadius: 12,
                    padding: 16,
                    marginBottom: 12,
                    borderLeftWidth: 4,
                    borderLeftColor: isCurrent ? '#3b82f6' : '#6b7280'
                }}
            >
                {/* Program başlığı ve zamanı */}
                <View style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: 8
                }}>
                    <Text
                        style={{
                            color: 'white',
                            fontSize: 16,
                            fontWeight: '600',
                            flex: 1,
                            marginRight: 12
                        }}
                        numberOfLines={2}
                    >
                        {cleanTitle}
                    </Text>
                    {isCurrent && (
                        <View style={{
                            backgroundColor: '#ef4444',
                            paddingHorizontal: 8,
                            paddingVertical: 4,
                            borderRadius: 6
                        }}>
                            <Text style={{color: 'white', fontSize: 10, fontWeight: '600'}}>
                                CANLI
                            </Text>
                        </View>
                    )}
                </View>

                {/* Zaman bilgileri */}
                <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 8}}>
                    <Ionicons name="time-outline" size={14} color="#9ca3af"/>
                    <Text style={{color: '#9ca3af', fontSize: 14, marginLeft: 6}}>
                        {startTime} - {endTime} ({duration})
                    </Text>
                </View>

                {/* Program açıklaması */}
                {cleanDescription && (
                    <Text
                        style={{
                            color: '#d1d5db',
                            fontSize: 14,
                            lineHeight: 20,
                            marginTop: 4
                        }}
                    >
                        {cleanDescription}
                    </Text>
                )}

                {/* Kategori bilgisi */}
                {program.category && (
                    <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 8}}>
                        <Ionicons name="pricetag-outline" size={14} color="#9ca3af"/>
                        <Text style={{color: '#9ca3af', fontSize: 12, marginLeft: 6}}>
                            {program.category}
                        </Text>
                    </View>
                )}
            </View>
        );
    };

    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="slide"
            onRequestClose={onClose}
            statusBarTranslucent={true}
        >
            <View style={{
                flex: 1,
                backgroundColor: 'rgba(0,0,0,0.8)',
                justifyContent: 'flex-end'
            }}>
                <View style={{
                    backgroundColor: '#1f2937',
                    borderTopLeftRadius: 24,
                    borderTopRightRadius: 24,
                    maxHeight: '85%',
                    minHeight: '50%'
                }}>
                    {/* Modal Header */}
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: 20,
                        borderBottomWidth: 1,
                        borderBottomColor: '#374151'
                    }}>
                        <View style={{flex: 1}}>
                            <Text style={{
                                color: 'white',
                                fontSize: 18,
                                fontWeight: 'bold',
                                marginBottom: 4
                            }} numberOfLines={1}>
                                {channel?.name || 'Kanal'}
                            </Text>
                            <Text style={{
                                color: '#9ca3af',
                                fontSize: 14
                            }}>
                                Program Rehberi
                            </Text>
                        </View>

                        <View style={{flexDirection: 'row', alignItems: 'center'}}>
                            {/* Yenile butonu */}
                            <TouchableOpacity
                                onPress={loadEPGData}
                                style={{
                                    padding: 8,
                                    marginRight: 8,
                                    borderRadius: 8,
                                    backgroundColor: '#374151'
                                }}
                                disabled={loading}
                            >
                                <Ionicons
                                    name="refresh"
                                    size={20}
                                    color={loading ? "#6b7280" : "#3b82f6"}
                                />
                            </TouchableOpacity>

                            {/* Kapat butonu */}
                            <TouchableOpacity
                                onPress={onClose}
                                style={{
                                    padding: 8,
                                    borderRadius: 8,
                                    backgroundColor: '#374151'
                                }}
                            >
                                <Ionicons name="close" size={20} color="#9ca3af"/>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Modal Content */}
                    <View style={{flex: 1}}>
                        {loading ? (
                            <View style={{
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                padding: 40
                            }}>
                                <ActivityIndicator size="large" color="#3b82f6"/>
                                <Text style={{
                                    color: '#9ca3af',
                                    fontSize: 16,
                                    marginTop: 16,
                                    textAlign: 'center'
                                }}>
                                    EPG verileri yükleniyor...
                                </Text>
                            </View>
                        ) : error ? (
                            <View style={{
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                padding: 40
                            }}>
                                <Ionicons name="alert-circle-outline" size={48} color="#ef4444"/>
                                <Text style={{
                                    color: '#ef4444',
                                    fontSize: 16,
                                    marginTop: 16,
                                    textAlign: 'center'
                                }}>
                                    {error}
                                </Text>
                                <TouchableOpacity
                                    onPress={loadEPGData}
                                    style={{
                                        backgroundColor: '#3b82f6',
                                        paddingHorizontal: 20,
                                        paddingVertical: 10,
                                        borderRadius: 8,
                                        marginTop: 16
                                    }}
                                >
                                    <Text style={{color: 'white', fontWeight: '600'}}>
                                        Tekrar Dene
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        ) : epgPrograms.length > 0 ? (
                            <ScrollView
                                style={{flex: 1}}
                                contentContainerStyle={{padding: 20}}
                                refreshControl={
                                    <RefreshControl
                                        refreshing={loading}
                                        onRefresh={loadEPGData}
                                        tintColor="#3b82f6"
                                    />
                                }
                            >
                                {/* Tarih başlığı */}
                                {epgPrograms.length > 0 && (
                                    <Text style={{
                                        color: '#3b82f6',
                                        fontSize: 16,
                                        fontWeight: '600',
                                        marginBottom: 16,
                                        textAlign: 'center'
                                    }}>
                                        {formatDate(epgPrograms[0].start_timestamp)}
                                    </Text>
                                )}

                                {/* Program listesi */}
                                {epgPrograms.map((program, index) => renderProgram(program, index))}
                            </ScrollView>
                        ) : (
                            <View style={{
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                padding: 40
                            }}>
                                <Ionicons name="tv-outline" size={48} color="#6b7280"/>
                                <Text style={{
                                    color: '#9ca3af',
                                    fontSize: 16,
                                    marginTop: 16,
                                    textAlign: 'center'
                                }}>
                                    Bu kanal için program bilgisi bulunamadı
                                </Text>
                            </View>
                        )}
                    </View>
                </View>
            </View>
        </Modal>
    );
};
