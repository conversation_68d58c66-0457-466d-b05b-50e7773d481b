import {useCallback, useEffect, useRef, useState} from 'react';
import {Al<PERSON>, StatusBar} from 'react-native';
import * as ScreenOrientation from 'expo-screen-orientation';
import {useVideoPlayer, VideoPlayer} from 'expo-video';
import {activateKeepAwakeAsync, deactivateKeep<PERSON>wake} from 'expo-keep-awake';
import {type Channel} from '../../utils/m3u-parser';
import {PreferencesStorage, RecentlyWatchedStorage, type UserPreferences} from '../../utils/storage';

export const useVideoPlayerLogic = (channels: Channel[], playlistId: string) => {
    const [selectedChannel, setSelectedChannel] = useState<Channel | null>(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [showControls, setShowControls] = useState(true);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [preferences, setPreferences] = useState<UserPreferences>(PreferencesStorage.getDefaultPreferences());
    const hideControlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const watchStartTimeRef = useRef<number>(Date.now());

    // Preferences'ları yükle
    useEffect(() => {
        const loadPreferences = async () => {
            try {
                const prefs = await PreferencesStorage.getPreferences();
                setPreferences(prefs);
            } catch (error) {
                console.error('Error loading preferences:', error);
            }
        };
        loadPreferences();
    }, []);

    // Video player oluştur - Player screen'dekine benzer yaklaşım
    const player = useVideoPlayer(
        selectedChannel?.url || '',
        useCallback((player: VideoPlayer) => {
            if (!selectedChannel?.url) return;

            try {
                player.loop = false;
                player.volume = 1;
                // AutoPlay ayarına göre player'ı başlat
                if (preferences.autoPlay) {
                    const initializePlayer = async () => {
                        try {
                            // Player'ın hazır olmasını bekle
                            await new Promise(resolve => setTimeout(resolve, 200));
                            player.play();
                            setIsPlaying(true);
                        } catch (playError) {
                            console.error('Play error:', playError);
                            // Tekrar deneme
                            setTimeout(() => {
                                try {
                                    player.play();
                                    setIsPlaying(true);
                                } catch (retryError) {
                                    console.error('Retry play error:', retryError);
                                }
                            }, 500);
                        }
                    };
                    void initializePlayer();
                } else {
                    // AutoPlay kapalıysa sadece hazırla ama oynatma
                    setIsPlaying(false);
                }
            } catch (error) {
                console.error('Video player initialization error:', error);
                Alert.alert(
                    'Oynatma Hatası',
                    'Video oynatılırken bir hata oluştu. Lütfen tekrar deneyin.',
                    [
                        {text: 'Kapat', onPress: () => setSelectedChannel(null)},
                        {
                            text: 'Tekrar Dene',
                            onPress: () => {
                                try {
                                    setTimeout(() => {
                                        player.play();
                                        setIsPlaying(true);
                                    }, 100);
                                } catch (retryError) {
                                    console.error('Retry failed:', retryError);
                                }
                            }
                        }
                    ]
                );
            }
        }, [selectedChannel?.url, preferences.autoPlay])
    );

    // Ekranı açık tutma - preferences'a göre
    useEffect(() => {
        if (preferences.keepScreenAwake && isPlaying && selectedChannel !== null) {
            activateKeepAwakeAsync('channels-player');
        } else {
            deactivateKeepAwake('channels-player');
        }
    }, [preferences.keepScreenAwake, isPlaying, selectedChannel]);

    // Kanal oynatma
    const playChannel = useCallback((channel: Channel) => {
        // Önceki player'ı tamamen durdur
        if (selectedChannel && player) {
            try {
                player.pause();
                player.volume = 0; // Sesi kes
                setIsPlaying(false);
                console.log('Previous channel stopped and muted');
            } catch (error) {
                console.log('Previous player already stopped:', error instanceof Error ? error.message : String(error));
            }
        }

        setSelectedChannel(channel);
        setIsPlaying(false); // Başlangıçta false, player initialize olunca true olacak
        setShowControls(true);
        setIsFullscreen(false); // Yeni kanal seçildiğinde normal moda dön
        watchStartTimeRef.current = Date.now();

        // Kontrolleri otomatik gizlemek için timer başlat
        resetHideControlsTimer();
    }, [selectedChannel, player]);

    // Kontrol timer'ı
    const resetHideControlsTimer = useCallback(() => {
        if (hideControlsTimeoutRef.current) {
            clearTimeout(hideControlsTimeoutRef.current);
        }
        setShowControls(true);
        hideControlsTimeoutRef.current = setTimeout(() => {
            setShowControls(false);
        }, 3000); // 3 saniye sonra gizle
    }, []);

    // Timer temizleme helper fonksiyonu
    const clearControlsTimer = useCallback(() => {
        if (hideControlsTimeoutRef.current) {
            clearTimeout(hideControlsTimeoutRef.current);
            hideControlsTimeoutRef.current = null;
        }
    }, []);

    // Video dokunma
    const handleVideoTouch = useCallback(() => {
        clearControlsTimer();

        if (showControls) {
            // Eğer kontroller görünüyorsa gizle
            setShowControls(false);
        } else {
            // Eğer kontroller gizliyse göster ve timer başlat
            resetHideControlsTimer();
        }
    }, [showControls, clearControlsTimer, resetHideControlsTimer]);

    // Oynat/Duraklat
    const togglePlayPause = useCallback(() => {
        try {
            if (isPlaying) {
                player?.pause();
                setIsPlaying(false);
            } else {
                player?.play();
                setIsPlaying(true);
            }
        } catch (error) {
            console.error('Play/pause error:', error);
        }

        // Kontrol butonuna basıldığında timer'ı yeniden başlat
        resetHideControlsTimer();
    }, [isPlaying, player, resetHideControlsTimer]);

    // Tam ekran geçiş - Expo Video'nun native tam ekran özelliğini kullan
    const toggleLandscape = useCallback(async () => {
        // Bu fonksiyon artık kullanılmayacak, Expo Video'nun kendi tam ekran butonunu kullanacağız
        console.log('Native fullscreen kullanılıyor');
    }, []);

    // Player'ı kapat
    const closePlayer = useCallback(async () => {
        // İzleme süresini kaydet
        if (selectedChannel) {
            try {
                const watchDuration = Date.now() - watchStartTimeRef.current;
                await RecentlyWatchedStorage.addRecentlyWatched(selectedChannel, playlistId, watchDuration);
            } catch (error) {
                console.error('Error saving recently watched:', error);
            }
        }

        // Player'ı tamamen durdur
        try {
            if (player) {
                player.pause();
                player.volume = 0; // Sesi kes
                console.log('Player stopped and muted');
            }
        } catch (error) {
            console.log('Player already stopped:', error instanceof Error ? error.message : String(error));
        }

        // State'leri sıfırla
        setSelectedChannel(null);
        setIsPlaying(false);
        setIsFullscreen(false);
        setShowControls(true);

        // Portrait moda dön ve orientation'ı unlock et
        try {
            await ScreenOrientation.unlockAsync();
            await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
        } catch (error) {
            console.error('Orientation reset error:', error);
        }

        // Timer'ı temizle
        clearControlsTimer();
    }, [selectedChannel, playlistId, player, clearControlsTimer]);

    // Sonraki kanal
    const goToNextChannel = useCallback(() => {
        if (!selectedChannel || !channels || channels.length === 0) return;

        const currentIndex = channels.findIndex(c => c.id === selectedChannel.id);
        if (currentIndex >= 0 && currentIndex < channels.length - 1) {
            playChannel(channels[currentIndex + 1]);
        }
        resetHideControlsTimer();
    }, [channels, selectedChannel, playChannel, resetHideControlsTimer]);

    // Önceki kanal
    const goToPreviousChannel = useCallback(() => {
        if (!selectedChannel || !channels || channels.length === 0) return;

        const currentIndex = channels.findIndex(c => c.id === selectedChannel.id);
        if (currentIndex > 0) {
            playChannel(channels[currentIndex - 1]);
        }
        resetHideControlsTimer();
    }, [channels, selectedChannel, playChannel, resetHideControlsTimer]);

    // Status bar kontrolü
    useEffect(() => {
        if (isFullscreen) {
            StatusBar.setHidden(true, 'fade');
        } else {
            StatusBar.setHidden(false, 'fade');
        }
    }, [isFullscreen]);

    // Cleanup - timer ve keep awake'i temizle
    useEffect(() => {
        return () => {
            // Timer'ı temizle
            clearControlsTimer();
            // Keep awake'i deaktive et
            deactivateKeepAwake('channels-player');
            // Player cleanup'ı React'ın kendi mekanizmasına bırak
        };
    }, [clearControlsTimer]);

    return {
        selectedChannel,
        isPlaying,
        showControls,
        isFullscreen,
        player,
        playChannel,
        handleVideoTouch,
        togglePlayPause,
        toggleLandscape,
        closePlayer,
        goToNextChannel,
        goToPreviousChannel
    };
};
