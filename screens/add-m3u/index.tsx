import React, {useEffect, useState} from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StatusBar,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Ionicons} from '@expo/vector-icons';
import {useNavigation} from '@react-navigation/native';
import * as ScreenOrientation from 'expo-screen-orientation';
import type {RootStackParamList} from '../../types/navigation';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {M3UParser} from "../../utils/m3u-parser";
import {PlaylistStorage} from "../../utils/storage";

type AddM3UScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'AddM3U'>;

const AddM3UScreen = () => {
    const navigation = useNavigation<AddM3UScreenNavigationProp>();
    const [playlistName, setPlaylistName] = useState('');
    const [playlistUrl, setPlaylistUrl] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [isTesting, setIsTesting] = useState(false);

    // Portrait modunu zorla
    useEffect(() => {
        const lockOrientation = async () => {
            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
                console.error('Orientation lock failed:', error);
            }
        };

        void lockOrientation();
    }, []);

    const validateUrl = (url: string) => {
        try {
            new URL(url);
            return url.toLowerCase().includes('.m3u') || url.toLowerCase().includes('m3u');
        } catch {
            return false;
        }
    };

    const testUrl = async () => {
        if (!playlistUrl.trim()) {
            Alert.alert('Hata', 'Lütfen test edilecek M3U URL\'sini girin.');
            return;
        }

        if (!validateUrl(playlistUrl)) {
            Alert.alert('Hata', 'Geçerli bir M3U URL\'si girin.');
            return;
        }

        setIsTesting(true);

        try {
            const playlist = await M3UParser.fetchAndParseM3U(playlistUrl.trim());

            let message = `URL başarıyla test edildi!\n\n📺 Toplam Kanal: ${playlist.totalChannels}\n📁 Grup Sayısı: ${playlist.groups.length}`;

            // Büyük playlist'ler için uyarı
            if (playlist.totalChannels > 10000) {
                message += '\n\n⚠️ Bu playlist çok büyük! Kanal yükleme işlemi uzun sürebilir ve depolama alanı dolabilir.';
            }

            Alert.alert(
                'Test Başarılı! ✅',
                message,
                [{text: 'Tamam'}]
            );
        } catch (error) {
            Alert.alert(
                'Test Başarısız ❌',
                `URL test edilirken hata oluştu:\n\n${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
                [{text: 'Tamam'}]
            );
        } finally {
            setIsTesting(false);
        }
    };

    const handleSave = async () => {
        if (!playlistName.trim()) {
            Alert.alert('Hata', 'Lütfen çalma listesi adını girin.');
            return;
        }

        if (!playlistUrl.trim()) {
            Alert.alert('Hata', 'Lütfen M3U URL\'sini girin.');
            return;
        }

        if (!validateUrl(playlistUrl)) {
            Alert.alert('Hata', 'Geçerli bir M3U URL\'si girin.');
            return;
        }

        setIsLoading(true);

        try {
            // Önce playlist boyutunu kontrol et
            const testPlaylist = await M3UParser.fetchAndParseM3U(playlistUrl.trim());

            if (testPlaylist.totalChannels > 15000) {
                Alert.alert(
                    'Büyük Playlist Uyarısı',
                    `Bu playlist ${testPlaylist.totalChannels} kanal içeriyor. Bu kadar büyük playlist'ler depolama sorunlarına neden olabilir.\n\nDevam etmek istediğinizden emin misiniz?`,
                    [
                        {text: 'İptal', style: 'cancel'},
                        {
                            text: 'Devam Et',
                            style: 'destructive',
                            onPress: async () => {
                                try {
                                    await PlaylistStorage.addPlaylist(playlistName.trim(), playlistUrl.trim());
                                    Alert.alert(
                                        'Başarılı',
                                        'M3U çalma listesi başarıyla eklendi!\n\n⚠️ İlk açılışta kanal yükleme uzun sürebilir.',
                                        [{text: 'Tamam', onPress: () => navigation.goBack()}]
                                    );
                                } catch (saveError) {
                                    Alert.alert('Hata', 'M3U çalma listesi eklenirken bir hata oluştu.');
                                }
                            }
                        }
                    ]
                );
                return;
            }

            // Normal boyuttaki playlist'ler için direkt kaydet
            await PlaylistStorage.addPlaylist(playlistName.trim(), playlistUrl.trim());

            Alert.alert(
                'Başarılı',
                'M3U çalma listesi başarıyla eklendi!',
                [
                    {
                        text: 'Tamam',
                        onPress: () => navigation.goBack()
                    }
                ]
            );
        } catch (error) {
            Alert.alert('Hata', 'M3U çalma listesi eklenirken bir hata oluştu.');
        } finally {
            setIsLoading(false);
        }
    };


    return (
        <SafeAreaView className="flex-1 bg-slate-900">
            <StatusBar barStyle="light-content" backgroundColor="#0f172a"/>

            <KeyboardAvoidingView
                className="flex-1"
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                    <View className="px-6 py-4">
                        {/* Header */}
                        <View className="flex-row items-center mb-8">
                            <TouchableOpacity
                                onPress={() => navigation.goBack()}
                                className="mr-4 p-2 -ml-2"
                            >
                                <Ionicons name="arrow-back" size={24} color="white"/>
                            </TouchableOpacity>
                            <Text className="text-white text-2xl font-bold">M3U Playlist Ekle</Text>
                        </View>

                        {/* Info Card */}
                        <View className="bg-blue-900/30 border border-blue-700/50 rounded-xl p-4 mb-6">
                            <View className="flex-row items-center mb-2">
                                <Ionicons name="information-circle" size={20} color="#3b82f6"/>
                                <Text className="text-blue-300 font-semibold ml-2">Bilgi</Text>
                            </View>
                            <Text className="text-blue-200 text-sm leading-5">
                                M3U formatındaki çalma listesi URL'nizi buraya ekleyerek IPTV kanallarınızı
                                izleyebilirsiniz.
                            </Text>
                        </View>

                        {/* Form */}
                        <View className="gap-6">
                            {/* Playlist Name Input */}
                            <View>
                                <Text className="text-white font-semibold text-base mb-3">
                                    Çalma Listesi Adı
                                </Text>
                                <View className="bg-slate-800 rounded-xl border border-slate-700">
                                    <TextInput
                                        className="text-white p-4 text-base"
                                        placeholder="Örn: Ana Kanallar"
                                        placeholderTextColor="#64748b"
                                        value={playlistName}
                                        onChangeText={setPlaylistName}
                                        editable={!isLoading}
                                    />
                                </View>
                            </View>

                            {/* URL Input */}
                            <View>
                                <Text className="text-white font-semibold text-base mb-3">
                                    M3U URL
                                </Text>
                                <View className="bg-slate-800 rounded-xl border border-slate-700">
                                    <TextInput
                                        className="text-white p-4 text-base"
                                        placeholder="https://example.com/playlist.m3u"
                                        placeholderTextColor="#64748b"
                                        value={playlistUrl}
                                        onChangeText={setPlaylistUrl}
                                        keyboardType="url"
                                        autoCapitalize="none"
                                        autoCorrect={false}
                                        editable={!isLoading}
                                        multiline
                                    />
                                </View>
                            </View>

                            {/* Test URL Button */}
                            <View>
                                <TouchableOpacity
                                    className={`rounded-xl p-4 flex-row items-center justify-center ${
                                        isTesting ? 'bg-orange-800' : 'bg-orange-600'
                                    } ${!playlistUrl.trim() ? 'opacity-50' : ''}`}
                                    onPress={testUrl}
                                    disabled={isTesting || !playlistUrl.trim()}
                                >
                                    {isTesting ? (
                                        <Ionicons name="hourglass-outline" size={20} color="white"/>
                                    ) : (
                                        <Ionicons name="checkmark-circle-outline" size={20} color="white"/>
                                    )}
                                    <Text className="text-white font-semibold text-base ml-2">
                                        {isTesting ? 'Test Ediliyor...' : 'URL\'yi Test Et'}
                                    </Text>
                                </TouchableOpacity>
                            </View>

                        </View>
                    </View>
                </ScrollView>

                {/* Bottom Action Buttons */}
                <View className="px-6 py-4 bg-slate-900 border-t border-slate-800">
                    <View className="flex-row gap-3">
                        <TouchableOpacity
                            className="flex-1 bg-slate-700 rounded-xl p-4 flex-row items-center justify-center"
                            onPress={() => navigation.goBack()}
                            disabled={isLoading || isTesting}
                        >
                            <Text className="text-slate-300 font-semibold text-base">İptal</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            className={`flex-1 rounded-xl p-4 flex-row items-center justify-center ${
                                isLoading ? 'bg-blue-800' : 'bg-blue-600'
                            }`}
                            onPress={handleSave}
                            disabled={isLoading || isTesting}
                        >
                            {isLoading ? (
                                <Ionicons name="hourglass-outline" size={20} color="white"/>
                            ) : (
                                <Ionicons name="save-outline" size={20} color="white"/>
                            )}
                            <Text className="text-white font-semibold text-base ml-2">
                                {isLoading ? 'Kaydediliyor...' : 'Kaydet'}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
};

export default AddM3UScreen;
