import {StatusBar} from 'expo-status-bar';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {SafeAreaProvider} from "react-native-safe-area-context";
import {NavigationContainer} from "@react-navigation/native";
import {useKeepAwake} from 'expo-keep-awake';

// Screens
import HomeScreen from "./home";
import PlaylistsScreen from "./playlists";
import SettingsScreen from "./settings";
import HelpScreen from "./help";
import AddM3UScreen from "./add-m3u";
import AddXtreamScreen from "./add-xtream";
import ChannelsScreen from "./channels";
import PlayerScreen from "./player";
import FavoritesScreen from "./favorites";
import RecentlyWatchedScreen from "./recently-watched";

import "../global.css";


export default function Root() {
    const Stack = createNativeStackNavigator();

    useKeepAwake();

    return (
        <SafeAreaProvider className="flex-1">
            <StatusBar style="auto"/>
            <NavigationContainer>
                <Stack.Navigator
                    screenOptions={{
                        headerStyle: {
                            backgroundColor: '#0f172a',
                        },
                        headerTintColor: '#ffffff',
                        headerTitleStyle: {
                            fontWeight: 'bold',
                        },
                    }}
                >
                    <Stack.Screen
                        name="Home"
                        component={HomeScreen}
                        options={{ headerShown: false }}
                    />
                    <Stack.Screen
                        name="Playlists"
                        component={PlaylistsScreen}
                        options={{ headerShown: false }}
                    />
                    <Stack.Screen
                        name="Settings"
                        component={SettingsScreen}
                        options={{ headerShown: false }}
                    />
                    <Stack.Screen
                        name="Help"
                        component={HelpScreen}
                        options={{ headerShown: false }}
                    />
                    <Stack.Screen
                        name="AddM3U"
                        component={AddM3UScreen}
                        options={{ headerShown: false }}
                    />
                    <Stack.Screen
                        name="AddXtream"
                        component={AddXtreamScreen}
                        options={{ headerShown: false }}
                    />
                    <Stack.Screen
                        name="Channels"
                        component={ChannelsScreen}
                        options={{ headerShown: false }}
                    />
                    <Stack.Screen
                        name="Player"
                        component={PlayerScreen}
                        options={{ headerShown: false }}
                    />
                    <Stack.Screen
                        name="Favorites"
                        component={FavoritesScreen}
                        options={{ headerShown: false }}
                    />
                    <Stack.Screen
                        name="RecentlyWatched"
                        component={RecentlyWatchedScreen}
                        options={{ headerShown: false }}
                    />
                </Stack.Navigator>
            </NavigationContainer>
        </SafeAreaProvider>
    );
}
