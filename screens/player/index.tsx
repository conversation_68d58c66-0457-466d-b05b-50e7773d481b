import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {<PERSON><PERSON>, BackHandler, StatusBar, TouchableOpacity, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import type {RouteProp} from '@react-navigation/native';
import {useFocusEffect, useNavigation, useRoute} from '@react-navigation/native';
import {useVideoPlayer, VideoPlayer, VideoView} from 'expo-video';
import {activateKeepAwakeAsync, deactivateKeepAwake} from 'expo-keep-awake';
import {Feather} from '@expo/vector-icons';
import * as ScreenOrientation from 'expo-screen-orientation';
import type {RootStackParamList} from '../../types/navigation';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {PreferencesStorage, RecentlyWatchedStorage, type UserPreferences} from '../../utils/storage';

type PlayerScreenRouteProp = RouteProp<RootStackParamList, 'Player'>;
type PlayerScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Player'>;

interface PlayerScreenProps {
}

const PlayerScreen: React.FC<PlayerScreenProps> = () => {
    const route = useRoute<PlayerScreenRouteProp>();
    const navigation = useNavigation<PlayerScreenNavigationProp>();
    const {channel, channels, playlistId} = route.params;

    const [currentChannel, setCurrentChannel] = useState(channel);

    const isMountedRef = useRef(true);

    const [isFullscreen, setIsFullscreen] = useState(false);
    const insets = useSafeAreaInsets();

    const [isPlaying, setIsPlaying] = useState(true);
    const [showControls, setShowControls] = useState(true);
    const [volume, setVolume] = useState(1.0);
    const [preferences, setPreferences] = useState<UserPreferences>(PreferencesStorage.getDefaultPreferences());

    const hideControlsTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    const watchStartTimeRef = useRef<number>(Date.now());

    // Preferences'ları yükle
    useEffect(() => {
        const loadPreferences = async () => {
            try {
                const prefs = await PreferencesStorage.getPreferences();
                setPreferences(prefs);
            } catch (error) {
                console.error('Error loading preferences:', error);
            }
        };
        loadPreferences();
    }, []);

    const playerConfig = useMemo(() => ({
        source: currentChannel.url,
        shouldPlay: preferences.autoPlay,
        isLooping: false,
    }), [currentChannel.url, preferences.autoPlay]);

    const player = useVideoPlayer(playerConfig.source, useCallback((player: VideoPlayer) => {
        try {
            player.loop = playerConfig.isLooping;
            if (playerConfig.shouldPlay) {
                player.play();
            }
        } catch (error) {
            console.error('Error initializing video player:', error);
            Alert.alert(
                'Oynatma Hatası',
                'Video oynatılırken bir hata oluştu. Lütfen tekrar deneyin.',
                [
                    {
                        text: 'Geri Dön',
                        onPress: () => navigation.goBack(),
                    },
                    {
                        text: 'Tekrar Dene',
                        onPress: () => {
                            try {
                                player.play();
                            } catch (retryError) {
                                console.error('Retry failed:', retryError);
                            }
                        },
                    },
                ]
            );
        }
    }, [playerConfig.isLooping, playerConfig.shouldPlay, navigation]));

    // Ekranı açık tutma - preferences'a göre
    useEffect(() => {
        if (preferences.keepScreenAwake && isPlaying) {
            activateKeepAwakeAsync('player-screen');
        } else {
            deactivateKeepAwake('player-screen');
        }
    }, [preferences.keepScreenAwake, isPlaying]);

    const saveRecentlyWatched = useCallback(async (channelToSave = currentChannel) => {
        try {
            const watchDuration = Math.floor((Date.now() - watchStartTimeRef.current) / 1000);
            await RecentlyWatchedStorage.addRecentlyWatched(channelToSave, playlistId, watchDuration);
        } catch (error) {
        }
    }, [currentChannel, playlistId]);

    useEffect(() => {
        isMountedRef.current = true;

        return () => {
            isMountedRef.current = false;

            if (currentChannel) {
                void saveRecentlyWatched(currentChannel);
            }

            // Player'ı tamamen durdur ve temizle
            try {
                if (player) {
                    player.pause();
                    player.volume = 0; // Sesi kes
                    console.log('Player stopped and muted on unmount');
                }
            } catch (error) {
                console.log('Player already stopped on unmount:', error instanceof Error ? error.message : String(error));
            }

            void ScreenOrientation.unlockAsync()

            // Keep awake'i deaktive et
            deactivateKeepAwake('player-screen');

            if (hideControlsTimeoutRef.current) {
                clearTimeout(hideControlsTimeoutRef.current);
            }
        };
    }, [player, currentChannel, saveRecentlyWatched]);

    useEffect(() => {
        navigation.setOptions({
            title: channel.name || 'Video Player',
        });
    }, [navigation, channel.name]);

    useEffect(() => {
        const initializeOrientation = async () => {
            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
            }
        };

        initializeOrientation();
    }, []);

    const resetHideControlsTimer = useCallback(() => {
        if (hideControlsTimeoutRef.current) {
            clearTimeout(hideControlsTimeoutRef.current);
        }

        setShowControls(true);
        hideControlsTimeoutRef.current = setTimeout(() => {
            setShowControls(false);
        }, 3000); // 3 saniye sonra gizle
    }, []);

    // Show controls on touch
    const handleVideoTouch = useCallback(() => {
        // Önce timer'ı temizle
        if (hideControlsTimeoutRef.current) {
            clearTimeout(hideControlsTimeoutRef.current);
            hideControlsTimeoutRef.current = null;
        }

        if (showControls) {
            // Eğer kontroller görünüyorsa gizle
            setShowControls(false);
        } else {
            // Eğer kontroller gizliyse göster ve timer başlat
            setShowControls(true);
            hideControlsTimeoutRef.current = setTimeout(() => {
                setShowControls(false);
            }, 3000);
        }
    }, [showControls]);

    const togglePlayPause = useCallback(() => {
        try {
            if (isPlaying) {
                player?.pause?.();
                setIsPlaying(false);
            } else {
                player?.play?.();
                setIsPlaying(true);
            }
        } catch (e) {
        }

        // Kontrol butonuna basıldığında sadece timer'ı yeniden başlat, kontrolleri gösterme
        if (hideControlsTimeoutRef.current) {
            clearTimeout(hideControlsTimeoutRef.current);
        }
        hideControlsTimeoutRef.current = setTimeout(() => {
            setShowControls(false);
        }, 3000);
    }, [isPlaying, player]);

    const handleVolumeChange = useCallback((newVolume: number) => {
        const v = Math.max(0, Math.min(1, newVolume));
        setVolume(v);
        player.volume = v;

        // Kontrol butonuna basıldığında sadece timer'ı yeniden başlat
        if (hideControlsTimeoutRef.current) {
            clearTimeout(hideControlsTimeoutRef.current);
        }
        hideControlsTimeoutRef.current = setTimeout(() => {
            setShowControls(false);
        }, 3000);
    }, [player]);

    const goToPrevChannel = useCallback(async () => {
        if (!channels || channels.length === 0) return;
        const idx = channels.findIndex(c => c.id === currentChannel.id);
        if (idx > 0) {
            await saveRecentlyWatched(currentChannel);

            setCurrentChannel(channels[idx - 1]);

            watchStartTimeRef.current = Date.now();
        }
        resetHideControlsTimer();
    }, [channels, currentChannel, resetHideControlsTimer, saveRecentlyWatched]);

    const goToNextChannel = useCallback(async () => {
        if (!channels || channels.length === 0) return;
        const idx = channels.findIndex(c => c.id === currentChannel.id);
        if (idx >= 0 && idx < channels.length - 1) {
            await saveRecentlyWatched(currentChannel);

            setCurrentChannel(channels[idx + 1]);

            watchStartTimeRef.current = Date.now();
        }
        resetHideControlsTimer();
    }, [channels, currentChannel, resetHideControlsTimer, saveRecentlyWatched]);

    useEffect(() => {
        navigation.setOptions({title: currentChannel.name || 'Video Player'});
        try {
            player.play();
            setIsPlaying(true);
        } catch {
        }

        watchStartTimeRef.current = Date.now();

        // Kontrolleri otomatik gizlemek için timer başlat
        resetHideControlsTimer();
    }, [currentChannel, navigation, player, resetHideControlsTimer]);

    useEffect(() => {
        const saveInitialChannel = async () => {
            try {
                await RecentlyWatchedStorage.addRecentlyWatched(currentChannel, playlistId, 0);
            } catch (error) {
            }
        };

        saveInitialChannel();
    }, []);

    const toggleFullscreen = useCallback(async () => {
        if (isFullscreen) {
            setIsFullscreen(false);

            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
            }
        } else {
            setIsFullscreen(true);

            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE_LEFT);
            } catch (error) {
            }
        }
        resetHideControlsTimer();
    }, [isFullscreen, resetHideControlsTimer]);

    // Geri butonuna basıldığında video oynatmayı devam ettir
    const handleBackPress = useCallback(async () => {
        try {
            // İzleme süresini kaydet
            await saveRecentlyWatched(currentChannel);

            // Mevcut player'ı tamamen durdur
            try {
                if (player) {
                    // Player'ı pause et
                    player.pause();
                    setIsPlaying(false);

                    // Player'ın volume'unu 0 yap (ses kesme)
                    player.volume = 0;

                    console.log('Player stopped and muted for navigation');
                }
            } catch (error) {
                console.log('Player already stopped or released:', error instanceof Error ? error.message : String(error));
            }

            // Channels screen'e geri dön ve mevcut kanalı oynatmaya devam et
            navigation.navigate('Channels', {
                playlistId,
                continuePlayback: {
                    channel: currentChannel,
                    isPlaying: false // Yeni player'da baştan başlasın
                }
            });
        } catch (error) {
            console.error('Error handling back press:', error);
            navigation.goBack();
        }
    }, [currentChannel, playlistId, navigation, saveRecentlyWatched, player]);

    // Hardware back button handler
    useFocusEffect(
        useCallback(() => {
            const onBackPress = () => {
                handleBackPress();
                return true; // Prevent default back action
            };

            const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
            return () => subscription.remove();
        }, [handleBackPress])
    );

    return (
        <View className="flex-1 bg-black">
            <StatusBar hidden={isFullscreen}/>

            <View className={isFullscreen ? "absolute inset-0 z-40" : "relative flex-1"}>
                <VideoView
                    style={{flex: 1}}
                    player={player}
                    allowsFullscreen={true}
                    allowsPictureInPicture={true}
                    allowsVideoFrameAnalysis={true}
                    contentFit="contain"
                    nativeControls={true}
                    showsTimecodes={true}
                    requiresLinearPlayback={false}
                    startsPictureInPictureAutomatically={false}
                />

                {/* Geri butonu overlay */}
                <View style={{position: 'absolute', left: 20, top: (insets.top || 0) + 16, zIndex: 50}}>
                    <TouchableOpacity
                        className="bg-black/60 rounded-full w-12 h-12 items-center justify-center"
                        onPress={handleBackPress}
                        activeOpacity={0.7}
                    >
                        <Feather name="arrow-left" size={24} color="white"/>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};


export default PlayerScreen;
