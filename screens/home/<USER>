import React, {useCallback, useEffect, useState} from 'react';
import {<PERSON>ert, FlatList, Image, ScrollView, StatusBar, Text, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from "react-native-safe-area-context";
import {Ionicons} from "@expo/vector-icons";
import {useFocusEffect, useNavigation} from "@react-navigation/native";
import * as ScreenOrientation from 'expo-screen-orientation';
import type {RootStackParamList} from "../../types/navigation";
import type {NativeStackNavigationProp} from "@react-navigation/native-stack";
import {
    type FavoriteChannel,
    FavoriteStorage,
    type Playlist,
    PlaylistStorage,
    type RecentlyWatched,
    RecentlyWatchedStorage
} from "../../utils/storage";

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

interface StoryItemProps {
    item: FavoriteChannel | RecentlyWatched;
    onPress: () => void;
}

const StoryItem: React.FC<StoryItemProps> = ({ item, onPress }) => (
    <TouchableOpacity
        className="items-center mr-3"
        onPress={onPress}
        style={{width: 80}}
        activeOpacity={0.7}
    >
        <View className="w-16 h-16 bg-slate-700 rounded-xl overflow-hidden border-2 border-blue-500 mb-2">
            {item.channelLogo ? (
                <Image
                    source={{ uri: item.channelLogo }}
                    className="w-full h-full"
                    resizeMode="cover"
                />
            ) : (
                <View className="w-full h-full items-center justify-center">
                    <Ionicons name="tv-outline" size={24} color="#64748b" />
                </View>
            )}
        </View>
        <Text className="text-white text-xs font-medium text-center" numberOfLines={1}>
            {item.channelName}
        </Text>
    </TouchableOpacity>
);

interface PlaylistCardProps {
    playlist: Playlist;
    onLivePress: () => void;
    onVODPress: () => void;
}

const PlaylistCard: React.FC<PlaylistCardProps> = ({playlist, onLivePress, onVODPress}) => (
    <View className="bg-slate-800 rounded-2xl mb-3 overflow-hidden border border-slate-700/50">
        {/* Header - Playlist bilgileri */}
        <View className="p-4 pb-3">
            <View className="flex-row items-center justify-between">
                <View className="flex-1">
                    <View className="flex-row items-center mb-1">
                        <View className={`w-3 h-3 rounded-full mr-2 ${
                            playlist.type === 'xtream' ? 'bg-purple-500' : 'bg-green-500'
                        }`}/>
                        <Text className="text-white font-semibold text-lg flex-1" numberOfLines={1}>
                            {playlist.name}
                        </Text>
                    </View>
                    {playlist.channelCount && (
                        <Text className="text-slate-400 text-sm ml-5">
                            {playlist.channelCount} kanal
                        </Text>
                    )}
                </View>

                <View className={`px-3 py-1 rounded-full ml-3 ${
                    playlist.type === 'xtream' ? 'bg-purple-500/20' : 'bg-green-500/20'
                }`}>
                    <Text className={`text-xs font-bold ${
                        playlist.type === 'xtream' ? 'text-purple-300' : 'text-green-300'
                    }`}>
                        {playlist.type === 'xtream' ? 'XTREAM' : 'M3U'}
                    </Text>
                </View>
            </View>
        </View>

        {/* Footer - Butonlar */}
        <View className="p-4">
            {playlist.type === 'xtream' ? (
                <View className="flex-row">
                    <TouchableOpacity
                        className="flex-1 bg-slate-700 rounded-l-lg py-3 items-center border-r border-slate-600"
                        onPress={onLivePress}
                        activeOpacity={0.8}
                    >
                        <Text className="text-white font-semibold text-sm">📺 CANLI</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="flex-1 bg-slate-700 rounded-r-lg py-3 items-center"
                        onPress={onVODPress}
                        activeOpacity={0.8}
                    >
                        <Text className="text-white font-semibold text-sm">🎬 VOD</Text>
                    </TouchableOpacity>
                </View>
            ) : (
                <TouchableOpacity
                    className="bg-slate-700 rounded-lg py-3 items-center"
                    onPress={onLivePress}
                    activeOpacity={0.8}
                >
                    <Text className="text-white font-semibold text-sm">📺 CANLI TV</Text>
                </TouchableOpacity>
            )}
        </View>
    </View>
);

const HomeScreen: React.FC = () => {
    const navigation = useNavigation<HomeScreenNavigationProp>();
    const [favorites, setFavorites] = useState<FavoriteChannel[]>([]);
    const [recentlyWatched, setRecentlyWatched] = useState<RecentlyWatched[]>([]);
    const [playlists, setPlaylists] = useState<Playlist[]>([]);

    // Portrait modunu zorla
    useEffect(() => {
        const lockOrientation = async () => {
            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
                console.error('Orientation lock failed:', error);
            }
        };

        void lockOrientation();
    }, []);

    const loadData = useCallback(async () => {
        try {
            const [favs, recent, playlistData] = await Promise.all([
                FavoriteStorage.getFavorites(),
                RecentlyWatchedStorage.getRecentlyWatched(),
                PlaylistStorage.getPlaylists()
            ]);
            setFavorites(favs.slice(0, 10));
            setRecentlyWatched(recent.slice(0, 10));
            setPlaylists(playlistData.slice(0, 6));
        } catch (error) {
        }
    }, []);


    const playFavoriteChannel = async (favorite: FavoriteChannel) => {
        try {
            const { ChannelStorage } = await import('../../utils/storage');
            const channels = await ChannelStorage.getChannels(favorite.playlistId);
            const channel = channels.find(ch => ch.id === favorite.channelId);

            if (channel) {
                navigation.navigate('Player', {
                    channel,
                    playlistId: favorite.playlistId,
                    channels: channels // Tüm kanalları gönder (kanal değiştirme için)
                });
            } else {
                Alert.alert('Hata', 'Kanal bulunamadı. Playlist güncellenmiş olabilir.');
            }
        } catch (error) {
            Alert.alert('Hata', 'Kanal oynatılırken bir hata oluştu.');
        }
    };

    const playRecentChannel = async (recent: RecentlyWatched) => {
        try {
            const { ChannelStorage } = await import('../../utils/storage');
            const channels = await ChannelStorage.getChannels(recent.playlistId);
            const channel = channels.find(ch => ch.id === recent.channelId);

            if (channel) {
                navigation.navigate('Player', {
                    channel,
                    playlistId: recent.playlistId,
                    channels: channels
                });
            } else {
                Alert.alert('Hata', 'Kanal bulunamadı. Playlist güncellenmiş olabilir.');
            }
        } catch (error) {
            Alert.alert('Hata', 'Kanal oynatılırken bir hata oluştu.');
        }
    };

    const openPlaylistLive = (playlist: Playlist) => {
        navigation.navigate('Channels', { playlistId: playlist.id });
    };

    const openPlaylistVOD = (playlist: Playlist) => {
        navigation.navigate('VOD', {playlistId: playlist.id});
    };

    useFocusEffect(
        useCallback(() => {
            void loadData();
        }, [loadData])
    );

    return (
        <SafeAreaView className="flex-1 bg-slate-900">
            <StatusBar barStyle="light-content" backgroundColor="#0f172a" />

            <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                {/* Sleek Brand Header */}
                <View className="px-6 pt-6 pb-3">
                    {/* Outer Card */}
                    <View className="bg-slate-800/90 rounded-xl p-1 shadow-2xl border border-slate-700/50">
                        <View className="relative overflow-hidden">
                            {/* Animated Background */}
                            <View className="bg-gradient-to-r from-indigo-900 via-blue-800 to-purple-900 rounded-lg">
                                {/* Floating Orbs */}
                                <View className="absolute top-2 right-4 w-8 h-8 bg-blue-400/20 rounded-full"></View>
                                <View className="absolute bottom-2 left-6 w-6 h-6 bg-purple-400/15 rounded-full"></View>

                                {/* Main Content */}
                                <View className="p-4 flex-row items-center justify-between">
                                    {/* Left Side - Logo & Brand */}
                                    <View className="flex-row items-center">
                                        {/* Hexagon Logo */}
                                        <View className="relative mr-3">
                                            <View
                                                className="w-12 h-12 bg-white/10 rounded-xl items-center justify-center border-2 border-blue-400/30">
                                                <View
                                                    className="w-8 h-8 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-lg items-center justify-center">
                                                    <Ionicons name="play" size={16} color="white"/>
                                                </View>
                                            </View>
                                            <View
                                                className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border border-white"></View>
                                        </View>

                                        {/* Brand Text */}
                                        <View>
                                            <Text className="text-white text-2xl font-black tracking-wide">NSM</Text>
                                            <Text className="text-white text-xs font-black tracking-widest mt-1">IPTV
                                                PLAYER</Text>
                                        </View>
                                    </View>

                                    {/* Right Side - Next Stream Media */}
                                    <View
                                        className="bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 px-3 py-1 rounded-lg shadow-lg border border-white/20">
                                        <Text className="text-blue-200 text-sm font-medium">
                                            <Text className="text-white font-bold">N</Text>ext{' '}
                                            <Text className="text-white font-bold">S</Text>tream{' '}
                                            <Text className="text-white font-bold">M</Text>edia
                                        </Text>
                                    </View>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>

                {favorites.length > 0 && (
                    <View className="mb-6 mt-6">
                        <View className="flex-row items-center justify-between px-6 mb-3">
                            <Text className="text-white text-xl font-bold">Favori Kanallar</Text>
                            <TouchableOpacity
                                onPress={() => navigation.navigate('Favorites')}
                                activeOpacity={1}
                            >
                                <Text className="text-slate-400 text-sm font-medium">Tümünü Gör</Text>
                            </TouchableOpacity>
                        </View>
                        <View className="px-6">
                            <FlatList
                                data={favorites}
                                renderItem={({item}) => (
                                    <StoryItem
                                        item={item}
                                        onPress={() => playFavoriteChannel(item)}
                                    />
                                )}
                                keyExtractor={(item) => item.id}
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                contentContainerStyle={{paddingRight: 24}}
                            />
                        </View>
                    </View>
                )}

                {recentlyWatched.length > 0 && (
                    <View className="mb-6 mt-6">
                        <View className="flex-row items-center justify-between px-6 mb-3">
                            <Text className="text-white text-xl font-bold">Son İzlenenler</Text>
                            <TouchableOpacity
                                onPress={() => navigation.navigate('RecentlyWatched')}
                                activeOpacity={1}
                            >
                                <Text className="text-slate-400 text-sm font-medium">Tümünü Gör</Text>
                            </TouchableOpacity>
                        </View>
                        <View className="px-6">
                            <FlatList
                                data={recentlyWatched}
                                renderItem={({item}) => (
                                    <StoryItem
                                        item={item}
                                        onPress={() => playRecentChannel(item)}
                                    />
                                )}
                                keyExtractor={(item) => item.id}
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                contentContainerStyle={{paddingRight: 24}}
                            />
                        </View>
                    </View>
                )}

                {playlists.length > 0 && (
                    <View className="mb-6 mt-6">
                        <View className="px-6 mb-3">
                            <Text className="text-white text-xl font-bold">Çalma Listelerim</Text>
                        </View>
                        <View className="px-6">
                            <FlatList
                                data={playlists}
                                renderItem={({item}) => (
                                    <PlaylistCard
                                        playlist={item}
                                        onLivePress={() => openPlaylistLive(item)}
                                        onVODPress={() => openPlaylistVOD(item)}
                                    />
                                )}
                                keyExtractor={(item) => item.id}
                                showsVerticalScrollIndicator={false}
                                scrollEnabled={false}
                            />
                        </View>
                    </View>
                )}

                <View className="px-6 mb-6 mt-6">
                    {/* Başlık */}
                    <Text className="text-white text-xl font-bold mb-4">Hızlı Erişim</Text>

                    <TouchableOpacity
                        className="bg-blue-600 rounded-xl p-4 flex-row items-center shadow-lg mb-4"
                        onPress={() => navigation.navigate('Playlists')}
                        style={{minHeight: 60}}
                    >
                        <View className="bg-white/20 w-10 h-10 rounded-lg items-center justify-center mr-4">
                            <Ionicons name="list" size={20} color="white"/>
                        </View>
                        <View className="flex-1">
                            <Text className="text-white font-bold text-base mb-1">Çalma Listelerim</Text>
                            <Text className="text-blue-100 text-xs leading-4">Playlist'leri yönet ve izle</Text>
                        </View>
                        <View className="bg-white/10 w-8 h-8 rounded-full items-center justify-center">
                            <Ionicons name="chevron-forward" size={16} color="white"/>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="bg-green-600 rounded-xl p-4 flex-row items-center shadow-lg mb-4"
                        onPress={() => navigation.navigate('AddM3U')}
                        style={{minHeight: 60}}
                    >
                        <View className="bg-white/20 w-10 h-10 rounded-lg items-center justify-center mr-4">
                            <Ionicons name="link" size={20} color="white"/>
                        </View>
                        <View className="flex-1">
                            <Text className="text-white font-bold text-base mb-1">M3U Playlist Ekle</Text>
                            <Text className="text-green-100 text-xs leading-4">URL ile playlist bağlantısı
                                ekleyin</Text>
                        </View>
                        <View className="bg-white/10 w-8 h-8 rounded-full items-center justify-center">
                            <Ionicons name="chevron-forward" size={16} color="white"/>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="bg-purple-600 rounded-xl p-4 flex-row items-center shadow-lg mb-4"
                        onPress={() => navigation.navigate('AddXtream')}
                        style={{minHeight: 60}}
                    >
                        <View className="bg-white/20 w-10 h-10 rounded-lg items-center justify-center mr-4">
                            <Ionicons name="server" size={20} color="white"/>
                        </View>
                        <View className="flex-1">
                            <Text className="text-white font-bold text-base mb-1">Xtream Codes API</Text>
                            <Text className="text-purple-100 text-xs leading-4">Sunucu bilgileri ile bağlantı
                                kurun</Text>
                        </View>
                        <View className="bg-white/10 w-8 h-8 rounded-full items-center justify-center">
                            <Ionicons name="chevron-forward" size={16} color="white"/>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="bg-orange-600 rounded-xl p-4 flex-row items-center shadow-lg mb-4"
                        onPress={() => navigation.navigate('Help')}
                        style={{minHeight: 60}}
                    >
                        <View className="bg-white/20 w-10 h-10 rounded-lg items-center justify-center mr-4">
                            <Ionicons name="help-circle" size={20} color="white"/>
                        </View>
                        <View className="flex-1">
                            <Text className="text-white font-bold text-base mb-1">Yardım</Text>
                            <Text className="text-orange-100 text-xs leading-4">Nasıl başlarım ve sık sorulan
                                sorular</Text>
                        </View>
                        <View className="bg-white/10 w-8 h-8 rounded-full items-center justify-center">
                            <Ionicons name="chevron-forward" size={16} color="white"/>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="bg-slate-700 rounded-xl p-4 flex-row items-center shadow-lg"
                        onPress={() => navigation.navigate('Settings')}
                        style={{minHeight: 60}}
                    >
                        <View className="bg-white/20 w-10 h-10 rounded-lg items-center justify-center mr-4">
                            <Ionicons name="settings" size={20} color="white"/>
                        </View>
                        <View className="flex-1">
                            <Text className="text-white font-bold text-base mb-1">Ayarlar</Text>
                            <Text className="text-slate-300 text-xs leading-4">Uygulama ayarları ve tercihler</Text>
                        </View>
                        <View className="bg-white/10 w-8 h-8 rounded-full items-center justify-center">
                            <Ionicons name="chevron-forward" size={16} color="white"/>
                        </View>
                    </TouchableOpacity>
                </View>


            </ScrollView>
        </SafeAreaView>
    );
};

export default HomeScreen;
