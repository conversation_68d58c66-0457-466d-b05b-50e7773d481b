import React, {useCallback, useEffect, useState} from 'react';
import {ActivityIndicator, Alert, ScrollView, StatusBar, Switch, Text, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Ionicons} from '@expo/vector-icons';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import * as ScreenOrientation from 'expo-screen-orientation';
import type {RootStackParamList} from '../../types/navigation';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {
    ChannelStorage,
    FavoriteStorage,
    PlaylistStorage,
    PreferencesStorage,
    RecentlyWatchedStorage,
    type UserPreferences
} from '../../utils/storage';

type SettingsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Settings'>;

const SettingsScreen = () => {
    const navigation = useNavigation<SettingsScreenNavigationProp>();
    const [preferences, setPreferences] = useState<UserPreferences>(PreferencesStorage.getDefaultPreferences());
    const [isLoading, setIsLoading] = useState(true);
    const [storageStats, setStorageStats] = useState<{
        totalKeys: number;
        channelKeys: number;
        estimatedSize: number
    } | null>(null);

    // Portrait modunu zorla
    useEffect(() => {
        const lockOrientation = async () => {
            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
                console.error('Orientation lock failed:', error);
            }
        };

        void lockOrientation();
    }, []);

    // Tercihleri yükle
    const loadPreferences = useCallback(async () => {
        try {
            setIsLoading(true);
            const prefs = await PreferencesStorage.getPreferences();
            setPreferences(prefs);
        } catch (error) {
            console.error('Tercihler yüklenirken hata:', error);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // Storage istatistiklerini yükle
    const loadStorageStats = useCallback(async () => {
        try {
            const stats = await ChannelStorage.getStorageStats();
            setStorageStats(stats);
        } catch (error) {
            console.error('Error loading storage stats:', error);
        }
    }, []);

    // Tercih güncelle
    const updatePreference = async (key: keyof UserPreferences, value: any) => {
        try {
            const updatedPrefs = { ...preferences, [key]: value };
            setPreferences(updatedPrefs);
            await PreferencesStorage.savePreferences({ [key]: value });
        } catch (error) {
            console.error('Tercih güncellenirken hata:', error);
            Alert.alert('Hata', 'Ayar kaydedilirken bir hata oluştu.');
        }
    };

    // Kanal verilerini temizle
    const clearChannelData = () => {
        Alert.alert(
            'Kanal Verilerini Temizle',
            'Bu işlem tüm kanal verilerini silecek ancak playlist\'ler korunacak. Kanallar tekrar yüklenebilir. Devam etmek istediğinizden emin misiniz?',
            [
                {
                    text: 'İptal',
                    style: 'cancel',
                },
                {
                    text: 'Temizle',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await ChannelStorage.clearAllChannels();
                            await loadStorageStats(); // İstatistikleri güncelle
                            Alert.alert('Başarılı', 'Kanal verileri temizlendi. Playlist\'ler korundu.');
                        } catch (error) {
                            Alert.alert('Hata', 'Kanal verileri temizlenirken bir hata oluştu.');
                        }
                    },
                },
            ]
        );
    };

    // Storage temizliği yap
    const cleanupStorage = async () => {
        try {
            await ChannelStorage.cleanupOldChannelData();
            await loadStorageStats(); // İstatistikleri güncelle
            Alert.alert('Başarılı', 'Storage temizliği tamamlandı.');
        } catch (error) {
            Alert.alert('Hata', 'Storage temizliği sırasında bir hata oluştu.');
        }
    };

    // Tüm verileri temizle
    const clearAllData = () => {
        Alert.alert(
            'Tüm Verileri Temizle',
            'Bu işlem tüm playlist\'leri, favorileri ve ayarları silecek. Devam etmek istediğinizden emin misiniz?',
            [
                {
                    text: 'İptal',
                    style: 'cancel',
                },
                {
                    text: 'Temizle',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await PlaylistStorage.clearAllPlaylists();
                            await FavoriteStorage.clearAllFavorites();
                            await RecentlyWatchedStorage.clearRecentlyWatched();
                            await PreferencesStorage.resetPreferences();

                            Alert.alert('Başarılı', 'Tüm veriler temizlendi.', [
                                {
                                    text: 'Tamam',
                                    onPress: () => navigation.goBack()
                                }
                            ]);
                        } catch (error) {
                            Alert.alert('Hata', 'Veriler temizlenirken bir hata oluştu.');
                        }
                    },
                },
            ]
        );
    };

    // Focus effect
    useFocusEffect(
        useCallback(() => {
            void loadPreferences();
            void loadStorageStats();
        }, [loadPreferences, loadStorageStats])
    );

    // Ayar item komponenti
    const SettingItem = ({
        icon,
        title,
        subtitle,
        value,
        onPress,
        type = 'switch'
    }: {
        icon: string;
        title: string;
        subtitle?: string;
        value?: boolean | string;
        onPress?: () => void;
        type?: 'switch' | 'button' | 'info';
    }) => (
        <TouchableOpacity
            className="bg-slate-800 rounded-xl p-4 mb-3 flex-row items-center"
            onPress={onPress}
            disabled={type === 'info'}
        >
            <View className="w-10 h-10 bg-slate-700 rounded-lg items-center justify-center mr-4">
                <Ionicons name={icon as any} size={20} color="#64748b" />
            </View>

            <View className="flex-1">
                <Text className="text-white font-semibold text-base mb-1">
                    {title}
                </Text>
                {subtitle && (
                    <Text className="text-slate-400 text-sm">
                        {subtitle}
                    </Text>
                )}
            </View>

            {type === 'switch' && (
                <Switch
                    value={value as boolean}
                    onValueChange={onPress}
                    trackColor={{ false: '#374151', true: '#3b82f6' }}
                    thumbColor={value ? '#ffffff' : '#9ca3af'}
                />
            )}

            {type === 'button' && (
                <Ionicons name="chevron-forward" size={20} color="#64748b" />
            )}

            {type === 'info' && (
                <Text className="text-slate-400 text-sm">
                    {value}
                </Text>
            )}
        </TouchableOpacity>
    );

    return (
        <SafeAreaView className="flex-1 bg-slate-900">
            <StatusBar barStyle="light-content" backgroundColor="#0f172a" />

            <View className="flex-1">
                {/* Header */}
                <View className="flex-row items-center px-4 py-3 border-b border-slate-800">
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        className="mr-4 p-2 -ml-2"
                    >
                        <Ionicons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <View className="flex-1">
                        <Text className="text-white text-xl font-bold">Ayarlar</Text>
                        <Text className="text-slate-400 text-sm">
                            Uygulama ayarları ve tercihler
                        </Text>
                    </View>
                </View>

                {isLoading ? (
                    <View className="flex-1 items-center justify-center">
                        <ActivityIndicator size="large" color="#3b82f6" />
                        <Text className="text-slate-400 text-base mt-4">Ayarlar yükleniyor...</Text>
                    </View>
                ) : (
                    <ScrollView className="flex-1 px-4 py-4" showsVerticalScrollIndicator={false}>
                {/* Video Ayarları */}
                <View className="mb-6">
                    <Text className="text-white text-lg font-bold mb-4">Video Ayarları</Text>

                    <SettingItem
                        icon="play-circle-outline"
                        title="Otomatik Oynatma"
                        subtitle="Kanal seçildiğinde hemen oynatmaya başlar. Kapalıysa manuel olarak play butonuna basmanız gerekir."
                        value={preferences.autoPlay}
                        onPress={() => updatePreference('autoPlay', !preferences.autoPlay)}
                    />

                    <SettingItem
                        icon="eye-outline"
                        title="Ekranı Açık Tut"
                        subtitle="Video izlerken telefonun ekranının otomatik kapanmasını engeller. Pil tasarrufu için kapatabilirsiniz."
                        value={preferences.keepScreenAwake}
                        onPress={() => updatePreference('keepScreenAwake', !preferences.keepScreenAwake)}
                    />


                </View>

                {/* Görünüm Ayarları */}
                <View className="mb-6">
                    <Text className="text-white text-lg font-bold mb-4">Görünüm Ayarları</Text>

                    <SettingItem
                        icon="image-outline"
                        title="Kanal Logolarını Göster"
                        subtitle="Kanal listesinde her kanalın logosunu gösterir. Kapatırsanız sadece kanal isimleri görünür ve daha hızlı yüklenir."
                        value={preferences.showChannelLogos}
                        onPress={() => updatePreference('showChannelLogos', !preferences.showChannelLogos)}
                    />

                    <SettingItem
                        icon="tv-outline"
                        title="EPG Göster"
                        subtitle="Program rehberini (şu anda ne yayınlandığı) gösterir. Kapatırsanız sadece kanal isimleri görünür."
                        value={preferences.showEPG}
                        onPress={() => updatePreference('showEPG', !preferences.showEPG)}
                    />
                </View>

                {/* Veri Yönetimi */}
                <View className="mb-6">
                    <Text className="text-white text-lg font-bold mb-4">Veri Yönetimi</Text>

                    <SettingItem
                        icon="heart-outline"
                        title="Favoriler"
                        subtitle="Beğendiğiniz kanalları favorilere eklemiş olabilirsiniz. Bu sayfada tüm favori kanallarınızı görebilirsiniz."
                        type="button"
                        onPress={() => navigation.navigate('Favorites')}
                    />

                    <SettingItem
                        icon="time-outline"
                        title="Son İzlenenler"
                        subtitle="Daha önce izlediğiniz kanalların listesi. Hızlıca tekrar izlemek için kullanabilirsiniz."
                        type="button"
                        onPress={() => navigation.navigate('RecentlyWatched')}
                    />

                    <SettingItem
                        icon="trash-outline"
                        title="Tüm Verileri Temizle"
                        subtitle="⚠️ DİKKAT: Tüm playlist'ler, favoriler, izleme geçmişi ve ayarlar silinir. Geri alınamaz!"
                        type="button"
                        onPress={clearAllData}
                    />
                </View>

                        {/* Storage Yönetimi */}
                        <View className="mb-6">
                            <Text className="text-white text-lg font-bold mb-4">Storage Yönetimi</Text>

                            {storageStats && (
                                <View className="bg-slate-800 rounded-xl p-4 mb-3 border border-slate-700">
                                    <View className="flex-row items-center mb-3">
                                        <Ionicons name="server-outline" size={24} color="#64748b"/>
                                        <Text className="text-white font-medium text-base ml-4">Depolama
                                            İstatistikleri</Text>
                                    </View>
                                    <Text className="text-slate-400 text-sm mb-2">
                                        📊 Uygulamanın telefonunuzda ne kadar yer kapladığını gösterir:
                                    </Text>
                                    <Text className="text-slate-400 text-sm mb-1">
                                        • Toplam Veri: {storageStats.totalKeys} adet
                                    </Text>
                                    <Text className="text-slate-400 text-sm mb-1">
                                        • Kanal Verisi: {storageStats.channelKeys} playlist
                                    </Text>
                                    <Text className="text-slate-400 text-sm">
                                        • Boyut: {(storageStats.estimatedSize / (1024 * 1024)).toFixed(2)} MB
                                    </Text>
                                </View>
                            )}

                            <SettingItem
                                icon="refresh-outline"
                                title="Akıllı Temizlik"
                                subtitle="🧹 Eski ve kullanılmayan kanal verilerini temizler. Playlist'ler ve ayarlar korunur. Güvenlidir."
                                type="button"
                                onPress={cleanupStorage}
                            />

                            <SettingItem
                                icon="trash-outline"
                                title="Kanal Verilerini Sıfırla"
                                subtitle="🔄 Tüm kanal verilerini siler, playlist'ler korunur. Kanallar tekrar yüklenebilir. Sorun yaşıyorsanız deneyin."
                                type="button"
                                onPress={clearChannelData}
                            />
                        </View>

                {/* Uygulama Bilgileri */}
                <View className="mb-6">
                    <Text className="text-white text-lg font-bold mb-4">Uygulama</Text>

                    <SettingItem
                        icon="help-circle-outline"
                        title="Yardım & Destek"
                        subtitle="📖 Uygulamayı nasıl kullanacağınızı öğrenin, playlist ekleme rehberi ve sık sorulan sorular"
                        type="button"
                        onPress={() => navigation.navigate('Help')}
                    />

                    <SettingItem
                        icon="information-circle-outline"
                        title="Uygulama Versiyonu"
                        value="1.0.0"
                        type="info"
                    />

                    <SettingItem
                        icon="code-outline"
                        title="Geliştirici"
                        value="Next Stream Media"
                        type="info"
                    />
                </View>
                </ScrollView>
                )}
            </View>
        </SafeAreaView>
    );
};

export default SettingsScreen;
