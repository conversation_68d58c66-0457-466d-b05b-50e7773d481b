import React, {useCallback, useEffect, useState} from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    Image,
    RefreshControl,
    StatusBar,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Ionicons} from '@expo/vector-icons';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import * as ScreenOrientation from 'expo-screen-orientation';
import type {RootStackParamList} from '../../types/navigation';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {ChannelStorage, type RecentlyWatched, RecentlyWatchedStorage} from '../../utils/storage';

type RecentlyWatchedScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'RecentlyWatched'>;

const RecentlyWatchedScreen = () => {
    const navigation = useNavigation<RecentlyWatchedScreenNavigationProp>();
    const [recentlyWatched, setRecentlyWatched] = useState<RecentlyWatched[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);

    // Portrait modunu zorla
    useEffect(() => {
        const lockOrientation = async () => {
            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
                console.error('Orientation lock failed:', error);
            }
        };

        void lockOrientation();
    }, []);

    // Son izlenenleri yükle
    const loadRecentlyWatched = useCallback(async () => {
        try {
            setIsLoading(true);
            const recent = await RecentlyWatchedStorage.getRecentlyWatched();
            setRecentlyWatched(recent);
        } catch (error) {
            console.error('Son izlenenler yüklenirken hata:', error);
            Alert.alert('Hata', 'Son izlenenler yüklenirken bir hata oluştu.');
        } finally {
            setIsLoading(false);
            setIsRefreshing(false);
        }
    }, []);

    // Kanalı oynat
    const playChannel = async (recent: RecentlyWatched) => {
        try {
            // Playlist kanallarını yükle
            const channels = await ChannelStorage.getChannels(recent.playlistId);
            const channel = channels.find(ch => ch.id === recent.channelId);

            if (channel) {
                navigation.navigate('Player', {
                    channel,
                    playlistId: recent.playlistId,
                    channels: channels // Tüm kanalları gönder (kanal değiştirme için)
                });
            } else {
                Alert.alert('Hata', 'Kanal bulunamadı. Playlist güncellenmiş olabilir.');
            }
        } catch (error) {
            console.error('Kanal oynatılırken hata:', error);
            Alert.alert('Hata', 'Kanal oynatılırken bir hata oluştu.');
        }
    };

    // Yenile
    const handleRefresh = () => {
        setIsRefreshing(true);
        void loadRecentlyWatched();
    };

    // Tek kanalı geçmişten kaldır
    const removeFromHistory = (item: RecentlyWatched) => {
        Alert.alert(
            'Kanalı Kaldır',
            `"${item.channelName}" kanalını geçmişten kaldırmak istediğinizden emin misiniz?`,
            [
                {
                    text: 'İptal',
                    style: 'cancel',
                },
                {
                    text: 'Kaldır',
                    style: 'destructive',
                    onPress: () => {
                        void (async () => {
                            try {
                                await RecentlyWatchedStorage.removeRecentlyWatched(item.id);
                                await loadRecentlyWatched();
                            } catch (error) {
                                Alert.alert('Hata', 'Kanal kaldırılırken bir hata oluştu.');
                            }
                        })();
                    },
                },
            ]
        );
    };

    // Tüm geçmişi temizle
    const clearAllHistory = () => {
        Alert.alert(
            'Geçmişi Temizle',
            'Tüm izleme geçmişini silmek istediğinizden emin misiniz?',
            [
                {
                    text: 'İptal',
                    style: 'cancel',
                },
                {
                    text: 'Temizle',
                    style: 'destructive',
                    onPress: () => {
                        void (async () => {
                            try {
                                await RecentlyWatchedStorage.clearRecentlyWatched();
                                await loadRecentlyWatched();
                            } catch (error) {
                                Alert.alert('Hata', 'Geçmiş temizlenirken bir hata oluştu.');
                            }
                        })();
                    },
                },
            ]
        );
    };

    // Zaman formatı
    const formatWatchTime = (watchedAt: string) => {
        const date = new Date(watchedAt);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'Az önce';
        if (diffMins < 60) return `${diffMins} dakika önce`;
        if (diffHours < 24) return `${diffHours} saat önce`;
        if (diffDays < 7) return `${diffDays} gün önce`;

        return date.toLocaleDateString('tr-TR');
    };

    // Focus effect
    useFocusEffect(
        useCallback(() => {
            void loadRecentlyWatched();
        }, [loadRecentlyWatched])
    );

    // Son izlenen item render
    const renderRecentItem = ({ item }: { item: RecentlyWatched }) => (
        <TouchableOpacity
            className="bg-slate-800 rounded-xl p-4 mb-3 flex-row items-center"
            onPress={() => playChannel(item)}
        >
            {/* Kanal logosu */}
            <View className="w-16 h-16 bg-slate-700 rounded-lg mr-4 overflow-hidden">
                {item.channelLogo ? (
                    <Image
                        source={{ uri: item.channelLogo }}
                        className="w-full h-full"
                        resizeMode="cover"
                    />
                ) : (
                    <View className="w-full h-full items-center justify-center">
                        <Ionicons name="tv-outline" size={24} color="#64748b" />
                    </View>
                )}
            </View>

            {/* Kanal bilgileri */}
            <View className="flex-1">
                <Text className="text-white font-semibold text-base mb-1" numberOfLines={1}>
                    {item.channelName}
                </Text>
                <Text className="text-slate-400 text-sm" numberOfLines={1}>
                    {formatWatchTime(item.watchedAt)}
                </Text>
                {item.duration && item.duration > 0 && (
                    <Text className="text-slate-500 text-xs mt-1">
                        {`${Math.floor(item.duration / 60)} dakika izlendi`}
                    </Text>
                )}
            </View>

            {/* Butonlar */}
            <View className="flex-row ml-2">
                {/* Kaldır butonu */}
                <TouchableOpacity
                    className="bg-red-600 rounded-lg p-3 mr-2"
                    onPress={() => removeFromHistory(item)}
                >
                    <Ionicons name="trash-outline" size={18} color="white"/>
                </TouchableOpacity>

                {/* Oynat butonu */}
                <TouchableOpacity
                    className="bg-blue-600 rounded-lg p-3"
                    onPress={() => playChannel(item)}
                >
                    <Ionicons name="play" size={20} color="white"/>
                </TouchableOpacity>
            </View>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView className="flex-1 bg-slate-900">
            <StatusBar barStyle="light-content" backgroundColor="#0f172a" />

            <View className="flex-1">
                {/* Header */}
                <View className="flex-row items-center justify-between px-4 py-3 border-b border-slate-800">
                    <View className="flex-row items-center">
                        <TouchableOpacity
                            onPress={() => navigation.goBack()}
                            className="mr-4 p-2 -ml-2"
                        >
                            <Ionicons name="arrow-back" size={24} color="white" />
                        </TouchableOpacity>
                        <View>
                            <Text className="text-white text-xl font-bold">Son İzlenenler</Text>
                            <Text className="text-slate-400 text-sm">
                                {`${recentlyWatched.length} kanal`}
                            </Text>
                        </View>
                    </View>

                    {recentlyWatched.length > 0 && (
                        <TouchableOpacity
                            onPress={clearAllHistory}
                            className="p-2"
                        >
                            <Ionicons name="trash-outline" size={24} color="#ef4444" />
                        </TouchableOpacity>
                    )}
                </View>

            {/* Son izlenenler listesi */}
            {isLoading ? (
                <View className="flex-1 items-center justify-center">
                    <ActivityIndicator size="large" color="#3b82f6" />
                    <Text className="text-slate-400 text-base mt-4">Son izlenenler yükleniyor...</Text>
                </View>
            ) : (
                <FlatList
                    data={recentlyWatched}
                    renderItem={renderRecentItem}
                    keyExtractor={(item) => item.id}
                    className="flex-1 px-4 py-4"
                    showsVerticalScrollIndicator={false}
                    refreshControl={
                        <RefreshControl
                            refreshing={isRefreshing}
                            onRefresh={handleRefresh}
                            tintColor="#3b82f6"
                        />
                    }
                    ListEmptyComponent={
                        <View className="flex-1 items-center justify-center py-20">
                            <View className="w-32 h-32 bg-slate-800 rounded-full items-center justify-center mb-6">
                                <Ionicons name="time-outline" size={64} color="#64748b" />
                            </View>
                            <Text className="text-white text-xl font-bold mb-2">
                                Henüz İzleme Geçmişi Yok
                            </Text>
                            <Text className="text-slate-400 text-base text-center px-8">
                                İzlediğiniz kanallar burada görünecek
                            </Text>
                        </View>
                    }
                />
            )}
            </View>
        </SafeAreaView>
    );
};

export default RecentlyWatchedScreen;
