import React, {useCallback, useEffect, useState} from 'react';
import {ActivityIndicator, Al<PERSON>, ScrollView, StatusBar, Text, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from "react-native-safe-area-context";
import {Ionicons} from "@expo/vector-icons";
import {useFocusEffect, useNavigation} from "@react-navigation/native";
import * as ScreenOrientation from 'expo-screen-orientation';
import type {RootStackParamList} from "../../types/navigation";
import type {NativeStackNavigationProp} from "@react-navigation/native-stack";
import {ChannelStorage, type Playlist, PlaylistStorage} from "../../utils/storage";

type PlaylistsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Playlists'>;

interface PlaylistCardProps {
    playlist: Playlist;
    onPlay: () => void;
    onDelete: () => void;
}

const PlaylistCard: React.FC<PlaylistCardProps> = ({ playlist, onPlay, onDelete }) => (
    <TouchableOpacity
        className="bg-slate-800 rounded-xl p-5 border border-slate-700 mb-4"
        onPress={onPlay}
    >
        <View className="flex-row items-center">
            <View className="flex-1">
                <View className="flex-row items-center mb-2">
                    <View className={`w-3 h-3 rounded-full mr-3 ${
                        playlist.type === 'xtream' ? 'bg-purple-500' : 'bg-blue-500'
                    }`} />
                    <Text className="text-white font-semibold text-lg flex-1" numberOfLines={1}>
                        {playlist.name}
                    </Text>
                </View>

                <Text className="text-slate-400 text-sm mb-3" numberOfLines={1}>
                    {playlist.type === 'xtream' ? 'Xtream Codes API' : 'M3U Playlist'}
                </Text>

                <View className="flex-row items-center justify-between">
                    <Text className="text-slate-500 text-xs">
                        {new Date(playlist.createdAt).toLocaleDateString('tr-TR')}
                    </Text>
                    {playlist.channelCount && (
                        <Text className="text-blue-400 text-xs font-medium">
                            {playlist.channelCount} kanal
                        </Text>
                    )}
                </View>
            </View>

            <View className="flex-row items-center gap-2 ml-4">
                <TouchableOpacity
                    className="bg-red-600 rounded-lg p-2"
                    onPress={(e) => {
                        e.stopPropagation();
                        onDelete();
                    }}
                >
                    <Ionicons name="trash" size={16} color="white" />
                </TouchableOpacity>

                <View className="bg-green-600 rounded-lg p-2">
                    <Ionicons name="play" size={16} color="white" />
                </View>
            </View>
        </View>
    </TouchableOpacity>
);

const PlaylistsScreen: React.FC = () => {
    const navigation = useNavigation<PlaylistsScreenNavigationProp>();
    const [playlists, setPlaylists] = useState<Playlist[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [storageStats, setStorageStats] = useState<{
        usagePercentage: number;
        estimatedSizeMB: number;
        maxSizeMB: number;
        playlistCount: number;
    } | null>(null);

    // Portrait modunu zorla
    useEffect(() => {
        const lockOrientation = async () => {
            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
                console.error('Orientation lock failed:', error);
            }
        };

        void lockOrientation();
    }, []);

    const loadStorageStats = useCallback(async () => {
        try {
            const stats = await ChannelStorage.getStorageStats();
            setStorageStats({
                usagePercentage: stats.usagePercentage,
                estimatedSizeMB: stats.estimatedSizeMB,
                maxSizeMB: stats.maxSizeMB,
                playlistCount: stats.playlistDetails.length
            });
        } catch (error) {
            console.error('Storage stats yüklenirken hata:', error);
        }
    }, []);

    const loadPlaylists = useCallback(async () => {
        try {
            setIsLoading(true);
            const savedPlaylists = await PlaylistStorage.getPlaylists();
            setPlaylists(savedPlaylists);
            await loadStorageStats();
        } catch (error) {
            console.error('Playlist\'ler yüklenirken hata:', error);
            Alert.alert('Hata', 'Playlist\'ler yüklenirken bir hata oluştu.');
        } finally {
            setIsLoading(false);
        }
    }, [loadStorageStats]);

    const handleDeletePlaylist = useCallback((playlist: Playlist) => {
        Alert.alert(
            'Playlist Sil',
            `"${playlist.name}" playlist'ini silmek istediğinizden emin misiniz?`,
            [
                {
                    text: 'İptal',
                    style: 'cancel',
                },
                {
                    text: 'Sil',
                    style: 'destructive',
                    onPress: () => {
                        void (async () => {
                            try {
                                await PlaylistStorage.deletePlaylist(playlist.id);
                                await loadPlaylists();
                                await loadStorageStats();
                            } catch (error) {
                                Alert.alert('Hata', 'Playlist silinirken bir hata oluştu.');
                            }
                        })();
                    },
                },
            ]
        );
    }, [loadPlaylists]);

    const handlePlayPlaylist = useCallback((playlist: Playlist) => {
        navigation.navigate('Channels', { playlistId: playlist.id });
    }, [navigation]);

    useFocusEffect(
        useCallback(() => {
            void loadPlaylists();
        }, [loadPlaylists])
    );

    if (isLoading) {
        return (
            <SafeAreaView className="flex-1 bg-slate-900">
                <StatusBar barStyle="light-content" backgroundColor="#0f172a" />
                <View className="flex-1 items-center justify-center">
                    <ActivityIndicator size="large" color="#3b82f6" />
                    <Text className="text-white text-lg mt-4">Yükleniyor...</Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView className="flex-1 bg-slate-900">
            <StatusBar barStyle="light-content" backgroundColor="#0f172a" />

            <View className="flex-1">
                <View className="flex-row items-center px-4 py-3 border-b border-slate-800">
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        className="mr-4 p-2 -ml-2"
                    >
                        <Ionicons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <View className="flex-1">
                        <Text className="text-white text-xl font-bold">Çalma Listelerim</Text>
                        <Text className="text-slate-400 text-sm">
                            {playlists.length} playlist
                        </Text>
                    </View>
                </View>

                {playlists.length === 0 ? (
                <View className="flex-1 items-center justify-center px-6">
                    <View className="items-center mb-12">
                        <View className="w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl items-center justify-center mb-6 shadow-lg">
                            <Ionicons name="list" size={64} color="white"/>
                        </View>
                        <Text className="text-white text-2xl font-bold text-center mb-2">
                            Henüz Playlist Yok
                        </Text>
                        <Text className="text-slate-400 text-base text-center mb-8">
                            İzlemeye başlamak için bir playlist ekleyin
                        </Text>
                    </View>

                    <View className="w-full mb-8">
                        <TouchableOpacity
                            className="bg-blue-600 rounded-xl p-5 flex-row items-center mb-4"
                            onPress={() => navigation.navigate('AddM3U')}
                        >
                            <View className="bg-white/20 w-12 h-12 rounded-lg items-center justify-center mr-4">
                                <Ionicons name="link-outline" size={24} color="white"/>
                            </View>
                            <View className="flex-1">
                                <Text className="text-white font-semibold text-lg">M3U Playlist Ekle</Text>
                                <Text className="text-blue-100 text-sm mt-1">URL ile playlist bağlantısı ekleyin</Text>
                            </View>
                            <Ionicons name="chevron-forward" size={20} color="white"/>
                        </TouchableOpacity>

                        <TouchableOpacity
                            className="bg-purple-600 rounded-xl p-5 flex-row items-center"
                            onPress={() => navigation.navigate('AddXtream')}
                        >
                            <View className="bg-white/20 w-12 h-12 rounded-lg items-center justify-center mr-4">
                                <Ionicons name="server-outline" size={24} color="white"/>
                            </View>
                            <View className="flex-1">
                                <Text className="text-white font-semibold text-lg">Xtream Codes API</Text>
                                <Text className="text-purple-100 text-sm mt-1">Sunucu bilgileri ile bağlantı kurun</Text>
                            </View>
                            <Ionicons name="chevron-forward" size={20} color="white"/>
                        </TouchableOpacity>
                    </View>
                </View>
            ) : (
                <ScrollView className="flex-1 px-4 py-4" showsVerticalScrollIndicator={false}>

                    <View className="flex-row mb-6 gap-4">
                        <TouchableOpacity
                            className="flex-1 bg-blue-600/20 border border-blue-600/30 rounded-xl p-4 flex-row items-center justify-center"
                            onPress={() => navigation.navigate('AddM3U')}
                        >
                            <Ionicons name="link-outline" size={20} color="#3b82f6"/>
                            <Text className="text-blue-400 font-medium ml-2">M3U Ekle</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            className="flex-1 bg-purple-600/20 border border-purple-600/30 rounded-xl p-4 flex-row items-center justify-center"
                            onPress={() => navigation.navigate('AddXtream')}
                        >
                            <Ionicons name="server-outline" size={20} color="#a855f7"/>
                            <Text className="text-purple-400 font-medium ml-2">Xtream</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Storage Durumu */}
                    {storageStats && (
                        <View className={`mb-4 p-3 rounded-xl border ${
                            storageStats.usagePercentage > 80
                                ? 'bg-red-900/30 border-red-700/50'
                                : storageStats.usagePercentage > 60
                                    ? 'bg-yellow-900/30 border-yellow-700/50'
                                    : 'bg-slate-800/50 border-slate-700/50'
                        }`}>
                            <View className="flex-row items-center justify-between mb-2">
                                <View className="flex-row items-center">
                                    <Ionicons
                                        name="server-outline"
                                        size={16}
                                        color={storageStats.usagePercentage > 80 ? '#ef4444' : storageStats.usagePercentage > 60 ? '#eab308' : '#64748b'}
                                    />
                                    <Text className={`ml-2 font-medium ${
                                        storageStats.usagePercentage > 80
                                            ? 'text-red-400'
                                            : storageStats.usagePercentage > 60
                                                ? 'text-yellow-400'
                                                : 'text-slate-400'
                                    }`}>
                                        Depolama Durumu
                                    </Text>
                                </View>
                                <Text className={`text-sm ${
                                    storageStats.usagePercentage > 80
                                        ? 'text-red-300'
                                        : storageStats.usagePercentage > 60
                                            ? 'text-yellow-300'
                                            : 'text-slate-300'
                                }`}>
                                    %{storageStats.usagePercentage}
                                </Text>
                            </View>
                            <View className={`h-2 rounded-full ${
                                storageStats.usagePercentage > 80
                                    ? 'bg-red-900/50'
                                    : storageStats.usagePercentage > 60
                                        ? 'bg-yellow-900/50'
                                        : 'bg-slate-700/50'
                            }`}>
                                <View
                                    className={`h-full rounded-full ${
                                        storageStats.usagePercentage > 80
                                            ? 'bg-red-500'
                                            : storageStats.usagePercentage > 60
                                                ? 'bg-yellow-500'
                                                : 'bg-blue-500'
                                    }`}
                                    style={{width: `${Math.min(storageStats.usagePercentage, 100)}%`}}
                                />
                            </View>
                            <Text className="text-slate-400 text-xs mt-1">
                                {storageStats.estimatedSizeMB} MB / {storageStats.maxSizeMB} MB kullanılıyor
                            </Text>
                        </View>
                    )}

                    <View className="pb-6">
                        {playlists.map((playlist) => (
                            <PlaylistCard
                                key={playlist.id}
                                playlist={playlist}
                                onPlay={() => handlePlayPlaylist(playlist)}
                                onDelete={() => handleDeletePlaylist(playlist)}
                            />
                        ))}
                    </View>
                </ScrollView>
            )}
            </View>
        </SafeAreaView>
    );
};

export default PlaylistsScreen;
