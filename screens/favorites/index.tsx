import React, {useCallback, useEffect, useState} from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    Image,
    RefreshControl,
    StatusBar,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Ionicons} from '@expo/vector-icons';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import * as ScreenOrientation from 'expo-screen-orientation';
import type {RootStackParamList} from '../../types/navigation';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {ChannelStorage, type FavoriteChannel, FavoriteStorage} from '../../utils/storage';

type FavoritesScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Favorites'>;

const FavoritesScreen = () => {
    const navigation = useNavigation<FavoritesScreenNavigationProp>();
    const [favorites, setFavorites] = useState<FavoriteChannel[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);

    // Portrait modunu zorla
    useEffect(() => {
        const lockOrientation = async () => {
            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
                console.error('Orientation lock failed:', error);
            }
        };

        void lockOrientation();
    }, []);

    const loadFavorites = useCallback(async () => {
        try {
            setIsLoading(true);
            const favoriteChannels = await FavoriteStorage.getFavorites();
            setFavorites(favoriteChannels);
        } catch (error) {
            Alert.alert('Hata', 'Favoriler yüklenirken bir hata oluştu.');
        } finally {
            setIsLoading(false);
            setIsRefreshing(false);
        }
    }, []);

    const removeFavorite = async (favorite: FavoriteChannel) => {
        Alert.alert(
            'Favoriden Kaldır',
            `"${favorite.channelName}" kanalını favorilerden kaldırmak istediğinizden emin misiniz?`,
            [
                {
                    text: 'İptal',
                    style: 'cancel',
                },
                {
                    text: 'Kaldır',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await FavoriteStorage.removeFavorite(favorite.channelId);
                            await loadFavorites();
                        } catch (error) {
                            Alert.alert('Hata', 'Favori kaldırılırken bir hata oluştu.');
                        }
                    },
                },
            ]
        );
    };

    const playChannel = async (favorite: FavoriteChannel) => {
        try {
            const channels = await ChannelStorage.getChannels(favorite.playlistId);
            const channel = channels.find(ch => ch.id === favorite.channelId);

            if (channel) {
                navigation.navigate('Player', {
                    channel,
                    playlistId: favorite.playlistId,
                    channels: [channel]
                });
            } else {
                Alert.alert('Hata', 'Kanal bulunamadı. Playlist güncellenmiş olabilir.');
            }
        } catch (error) {
            Alert.alert('Hata', 'Kanal oynatılırken bir hata oluştu.');
        }
    };

    const handleRefresh = () => {
        setIsRefreshing(true);
        loadFavorites();
    };

    const clearAllFavorites = () => {
        Alert.alert(
            'Tüm Favorileri Temizle',
            'Tüm favori kanalları silmek istediğinizden emin misiniz?',
            [
                {
                    text: 'İptal',
                    style: 'cancel',
                },
                {
                    text: 'Temizle',
                    style: 'destructive',
                    onPress: () => {
                        void (async () => {
                            try {
                                await FavoriteStorage.clearAllFavorites();
                                await loadFavorites();
                            } catch (error) {
                                Alert.alert('Hata', 'Favoriler temizlenirken bir hata oluştu.');
                            }
                        })();
                    },
                },
            ]
        );
    };

    useFocusEffect(
        useCallback(() => {
            void loadFavorites();
        }, [loadFavorites])
    );

    const renderFavoriteItem = ({item}: { item: FavoriteChannel }) => (
        <TouchableOpacity
            className="bg-slate-800 rounded-xl p-4 mb-3 flex-row items-center"
            onPress={() => playChannel(item)}
        >
            <View className="w-16 h-16 bg-slate-700 rounded-lg mr-4 overflow-hidden">
                {item.channelLogo ? (
                    <Image
                        source={{uri: item.channelLogo}}
                        className="w-full h-full"
                        resizeMode="cover"
                    />
                ) : (
                    <View className="w-full h-full items-center justify-center">
                        <Ionicons name="tv-outline" size={24} color="#64748b"/>
                    </View>
                )}
            </View>

            <View className="flex-1">
                <Text className="text-white font-semibold text-base mb-1" numberOfLines={1}>
                    {item.channelName}
                </Text>
                <Text className="text-slate-400 text-sm" numberOfLines={1}>
                    {new Date(item.addedAt).toLocaleDateString('tr-TR')} tarihinde eklendi
                </Text>
            </View>

            <TouchableOpacity
                className="p-2 ml-2"
                onPress={() => removeFavorite(item)}
            >
                <Ionicons name="heart" size={24} color="#ef4444"/>
            </TouchableOpacity>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView className="flex-1 bg-slate-900">
            <StatusBar barStyle="light-content" backgroundColor="#0f172a"/>

            <View className="flex-1">
                <View className="flex-row items-center justify-between px-4 py-3 border-b border-slate-800">
                    <View className="flex-row items-center">
                        <TouchableOpacity
                            onPress={() => navigation.goBack()}
                            className="mr-4 p-2 -ml-2"
                        >
                            <Ionicons name="arrow-back" size={24} color="white"/>
                        </TouchableOpacity>
                        <View>
                            <Text className="text-white text-xl font-bold">Favoriler</Text>
                            <Text className="text-slate-400 text-sm">
                                {favorites.length} favori kanal
                            </Text>
                        </View>
                    </View>

                    {favorites.length > 0 && (
                        <TouchableOpacity
                            onPress={clearAllFavorites}
                            className="p-2"
                        >
                            <Ionicons name="trash-outline" size={24} color="#ef4444"/>
                        </TouchableOpacity>
                    )}
                </View>

                {isLoading ? (
                    <View className="flex-1 items-center justify-center">
                        <ActivityIndicator size="large" color="#3b82f6" />
                        <Text className="text-slate-400 text-base mt-4">Favoriler yükleniyor...</Text>
                    </View>
                ) : (
                    <FlatList
                        data={favorites}
                        renderItem={renderFavoriteItem}
                        keyExtractor={(item) => item.id}
                        className="flex-1 px-4 py-4"
                        showsVerticalScrollIndicator={false}
                        refreshControl={
                            <RefreshControl
                                refreshing={isRefreshing}
                                onRefresh={handleRefresh}
                                tintColor="#3b82f6"
                            />
                        }
                        ListEmptyComponent={
                            <View className="flex-1 items-center justify-center py-20">
                                <View className="w-32 h-32 bg-slate-800 rounded-full items-center justify-center mb-6">
                                    <Ionicons name="heart-outline" size={64} color="#64748b"/>
                                </View>
                                <Text className="text-white text-xl font-bold mb-2">
                                    Henüz Favori Yok
                                </Text>
                                <Text className="text-slate-400 text-base text-center px-8">
                                    Kanal listesinden beğendiğiniz kanalları favorilere ekleyebilirsiniz
                                </Text>
                            </View>
                        }
                    />
                )}
            </View>
        </SafeAreaView>
    );
};

export default FavoritesScreen;
