import React, {useEffect} from 'react';
import {ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from "react-native-safe-area-context";
import {Ionicons} from "@expo/vector-icons";
import {useNavigation} from "@react-navigation/native";
import * as ScreenOrientation from 'expo-screen-orientation';
import type {NativeStackNavigationProp} from "@react-navigation/native-stack";
import type {RootStackParamList} from "../../types/navigation";

type HelpScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Help'>;

const HelpScreen: React.FC = () => {
    const navigation = useNavigation<HelpScreenNavigationProp>();

    // Portrait modunu zorla
    useEffect(() => {
        const lockOrientation = async () => {
            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
                console.error('Orientation lock failed:', error);
            }
        };

        void lockOrientation();
    }, []);

    return (
        <SafeAreaView className="flex-1 bg-slate-900">
            <View className="flex-1">
                {/* Header */}
                <View className="flex-row items-center px-6 py-4 border-b border-slate-700/50">
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        className="w-10 h-10 items-center justify-center mr-4"
                    >
                        <Ionicons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <Text className="text-white text-xl font-bold">Yardım</Text>
                </View>

                <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                    {/* Getting Started Guide */}
                    <View className="px-6 py-6">
                        <View className="bg-slate-800/60 border border-slate-700/50 rounded-2xl p-6">
                            <View className="flex-row items-center mb-6">
                                <View className="w-12 h-12 bg-blue-600/20 rounded-full items-center justify-center mr-4">
                                    <Ionicons name="information-circle" size={28} color="#3b82f6" />
                                </View>
                                <Text className="text-white font-bold text-xl">Nasıl Başlarım?</Text>
                            </View>

                            <View className="gap-5">
                                {[
                                    {
                                        step: 1,
                                        title: 'Bilgilerinizi Hazırlayın',
                                        description: 'M3U URL\'nizi veya Xtream Codes bilgilerinizi hazırlayın'
                                    },
                                    {
                                        step: 2,
                                        title: 'Bağlantı Kurun',
                                        description: 'Ana sayfadaki butonlardan birini seçerek bilgilerinizi girin'
                                    },
                                    {
                                        step: 3,
                                        title: 'İzlemeye Başlayın',
                                        description: 'Kanallarınız yüklendikten sonra izlemeye başlayın!'
                                    }
                                ].map((item, index) => (
                                    <View key={index} className="flex-row items-start">
                                        <View className="w-8 h-8 bg-blue-600 rounded-full items-center justify-center mr-4 mt-1">
                                            <Text className="text-white font-bold text-sm">{item.step}</Text>
                                        </View>
                                        <View className="flex-1">
                                            <Text className="text-white font-semibold text-base mb-1">{item.title}</Text>
                                            <Text className="text-slate-300 text-sm leading-5">{item.description}</Text>
                                        </View>
                                    </View>
                                ))}
                            </View>
                        </View>
                    </View>

                    {/* M3U URL Hakkında */}
                    <View className="px-6 pb-6">
                        <View className="bg-slate-800/60 border border-slate-700/50 rounded-2xl p-6">
                            <View className="flex-row items-center mb-4">
                                <View className="w-10 h-10 bg-green-600/20 rounded-full items-center justify-center mr-4">
                                    <Ionicons name="link" size={24} color="#10b981" />
                                </View>
                                <Text className="text-white font-bold text-lg">M3U URL Nedir?</Text>
                            </View>
                            <Text className="text-slate-300 text-sm leading-6 mb-4">
                                M3U, multimedya dosyalarının listesini içeren bir dosya formatıdır. IPTV sağlayıcınızdan aldığınız M3U URL'si, tüm kanal listesini içerir.
                            </Text>
                            <View className="bg-slate-700/50 rounded-lg p-4">
                                <Text className="text-slate-400 text-xs mb-2">Örnek M3U URL:</Text>
                                <Text className="text-blue-400 text-xs font-mono">
                                    http://example.com/playlist.m3u8
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Xtream Codes Hakkında */}
                    <View className="px-6 pb-6">
                        <View className="bg-slate-800/60 border border-slate-700/50 rounded-2xl p-6">
                            <View className="flex-row items-center mb-4">
                                <View className="w-10 h-10 bg-purple-600/20 rounded-full items-center justify-center mr-4">
                                    <Ionicons name="server" size={24} color="#8b5cf6" />
                                </View>
                                <Text className="text-white font-bold text-lg">Xtream Codes API</Text>
                            </View>
                            <Text className="text-slate-300 text-sm leading-6 mb-4">
                                Xtream Codes, IPTV sağlayıcıları tarafından kullanılan popüler bir panel sistemidir. Bu yöntemle bağlanmak için sunucu bilgilerine ihtiyacınız vardır.
                            </Text>
                            <View className="bg-slate-700/50 rounded-lg p-4 gap-3">
                                <View>
                                    <Text className="text-slate-400 text-xs mb-1">Sunucu URL:</Text>
                                    <Text className="text-blue-400 text-xs font-mono">http://example.com:8080</Text>
                                </View>
                                <View>
                                    <Text className="text-slate-400 text-xs mb-1">Kullanıcı Adı:</Text>
                                    <Text className="text-blue-400 text-xs font-mono">your_username</Text>
                                </View>
                                <View>
                                    <Text className="text-slate-400 text-xs mb-1">Şifre:</Text>
                                    <Text className="text-blue-400 text-xs font-mono">your_password</Text>
                                </View>
                            </View>
                        </View>
                    </View>

                    {/* Sık Sorulan Sorular */}
                    <View className="px-6 pb-10">
                        <View className="bg-slate-800/60 border border-slate-700/50 rounded-2xl p-6">
                            <View className="flex-row items-center mb-6">
                                <View className="w-10 h-10 bg-orange-600/20 rounded-full items-center justify-center mr-4">
                                    <Ionicons name="help-circle" size={24} color="#f97316" />
                                </View>
                                <Text className="text-white font-bold text-lg">Sık Sorulan Sorular</Text>
                            </View>

                            <View className="gap-4">
                                <View>
                                    <Text className="text-white font-semibold text-sm mb-2">Kanallar yüklenmiyor, ne yapmalıyım?</Text>
                                    <Text className="text-slate-300 text-xs leading-5">
                                        URL'nizin doğru olduğundan ve internet bağlantınızın aktif olduğundan emin olun. Sorun devam ederse IPTV sağlayıcınızla iletişime geçin.
                                    </Text>
                                </View>

                                <View>
                                    <Text className="text-white font-semibold text-sm mb-2">Video oynatılmıyor?</Text>
                                    <Text className="text-slate-300 text-xs leading-5">
                                        İnternet bağlantınızı kontrol edin ve farklı bir kanal deneyin. Bazı kanallar belirli saatlerde yayın yapmayabilir.
                                    </Text>
                                </View>

                                <View>
                                    <Text className="text-white font-semibold text-sm mb-2">Favorilerim nasıl çalışır?</Text>
                                    <Text className="text-slate-300 text-xs leading-5">
                                        Kanal listesinde kalp ikonuna tıklayarak kanalları favorilerinize ekleyebilirsiniz. Favorilerinize ana sayfadan veya ayarlardan erişebilirsiniz.
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </View>
                </ScrollView>
            </View>
        </SafeAreaView>
    );
};

export default HelpScreen;
