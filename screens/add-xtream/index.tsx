import React, {useEffect, useState} from 'react';
import {
    ActivityIndicator,
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StatusBar,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Ionicons} from '@expo/vector-icons';
import {useNavigation} from '@react-navigation/native';
import * as ScreenOrientation from 'expo-screen-orientation';
import type {RootStackParamList} from '../../types/navigation';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {XtreamAPI, type XtreamCredentials} from "../../utils/xtream-api";
import {PlaylistStorage} from "../../utils/storage";

type AddXtreamScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'AddXtream'>;

const AddXtreamScreen = () => {
    const navigation = useNavigation<AddXtreamScreenNavigationProp>();
    const [playlistName, setPlaylistName] = useState('');
    const [serverUrl, setServerUrl] = useState('');
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');

    // Portrait modunu zorla
    useEffect(() => {
        const lockOrientation = async () => {
            try {
                await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            } catch (error) {
                console.error('Orientation lock failed:', error);
            }
        };

        void lockOrientation();
    }, []);
    const [isLoading, setIsLoading] = useState(false);
    const [isTestingConnection, setIsTestingConnection] = useState(false);

    const validateInputs = () => {
        if (!playlistName.trim()) {
            Alert.alert('Hata', 'Lütfen çalma listesi adını girin.');
            return false;
        }

        if (!serverUrl.trim()) {
            Alert.alert('Hata', 'Lütfen sunucu URL\'sini girin.');
            return false;
        }

        if (!username.trim()) {
            Alert.alert('Hata', 'Lütfen kullanıcı adını girin.');
            return false;
        }

        if (!password.trim()) {
            Alert.alert('Hata', 'Lütfen şifreyi girin.');
            return false;
        }

        if (!XtreamAPI.validateServerUrl(serverUrl)) {
            Alert.alert('Hata', 'Geçerli bir sunucu URL\'si girin (http:// veya https:// ile başlamalı).');
            return false;
        }

        return true;
    };

    const testConnection = async () => {
        if (!validateInputs()) return;

        setIsTestingConnection(true);

        try {
            const credentials: XtreamCredentials = {
                serverUrl: serverUrl.trim(),
                username: username.trim(),
                password: password.trim(),
            };

            const xtreamAPI = new XtreamAPI(credentials);
            const authInfo = await xtreamAPI.authenticate();

            Alert.alert(
                'Test Başarılı! ✅',
                `Bağlantı başarıyla test edildi!\n\n👤 Kullanıcı: ${authInfo.user_info.username}\n📊 Durum: ${authInfo.user_info.status}\n📅 Son Kullanma: ${new Date(parseInt(authInfo.user_info.exp_date) * 1000).toLocaleDateString('tr-TR')}`,
                [{ text: 'Tamam' }]
            );
        } catch (error) {
            Alert.alert(
                'Test Başarısız ❌',
                `Bağlantı test edilirken hata oluştu:\n\n${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
                [{ text: 'Tamam' }]
            );
        } finally {
            setIsTestingConnection(false);
        }
    };

    const handleSave = async () => {
        if (!validateInputs()) return;

        setIsLoading(true);

        try {
            const credentials: XtreamCredentials = {
                serverUrl: serverUrl.trim(),
                username: username.trim(),
                password: password.trim(),
            };

            // Önce bağlantıyı test et
            const xtreamAPI = new XtreamAPI(credentials);
            await xtreamAPI.authenticate();

            // Playlist'i kaydet
            await PlaylistStorage.addXtreamPlaylist(playlistName.trim(), credentials);

            Alert.alert(
                'Başarılı',
                'Xtream Codes bağlantısı başarıyla eklendi!',
                [
                    {
                        text: 'Tamam',
                        onPress: () => navigation.goBack()
                    }
                ]
            );
        } catch (error) {
            Alert.alert('Hata', error instanceof Error ? error.message : 'Xtream bağlantısı eklenirken bir hata oluştu.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <SafeAreaView className="flex-1 bg-slate-900">
            <StatusBar barStyle="light-content" backgroundColor="#0f172a" />

            <KeyboardAvoidingView
                className="flex-1"
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                    <View className="px-6 py-4">
                        {/* Header */}
                        <View className="flex-row items-center mb-8">
                            <TouchableOpacity
                                onPress={() => navigation.goBack()}
                                className="mr-4 p-2 -ml-2"
                            >
                                <Ionicons name="arrow-back" size={24} color="white" />
                            </TouchableOpacity>
                            <Text className="text-white text-2xl font-bold">Xtream Codes Ekle</Text>
                        </View>

                        {/* Info Card */}
                        <View className="bg-purple-900/30 border border-purple-700/50 rounded-xl p-4 mb-6">
                            <View className="flex-row items-center mb-2">
                                <Ionicons name="information-circle" size={20} color="#a855f7" />
                                <Text className="text-purple-300 font-semibold ml-2">Bilgi</Text>
                            </View>
                            <Text className="text-purple-200 text-sm leading-5">
                                Xtream Codes API bilgilerinizi girerek IPTV sağlayıcınızdan kanalları otomatik olarak yükleyebilirsiniz.
                            </Text>
                        </View>

                        {/* Form */}
                        <View className="gap-6">
                            {/* Playlist Name Input */}
                            <View>
                                <Text className="text-white font-semibold text-base mb-3">
                                    Çalma Listesi Adı
                                </Text>
                                <View className="bg-slate-800 rounded-xl border border-slate-700">
                                    <TextInput
                                        className="text-white p-4 text-base"
                                        placeholder="Örn: IPTV Sağlayıcım"
                                        placeholderTextColor="#64748b"
                                        value={playlistName}
                                        onChangeText={setPlaylistName}
                                        editable={!isLoading && !isTestingConnection}
                                    />
                                </View>
                            </View>

                            {/* Server URL Input */}
                            <View>
                                <Text className="text-white font-semibold text-base mb-3">
                                    Sunucu URL
                                </Text>
                                <View className="bg-slate-800 rounded-xl border border-slate-700">
                                    <TextInput
                                        className="text-white p-4 text-base"
                                        placeholder="http://example.com:8080"
                                        placeholderTextColor="#64748b"
                                        value={serverUrl}
                                        onChangeText={setServerUrl}
                                        keyboardType="url"
                                        autoCapitalize="none"
                                        autoCorrect={false}
                                        editable={!isLoading && !isTestingConnection}
                                    />
                                </View>
                            </View>

                            {/* Username Input */}
                            <View>
                                <Text className="text-white font-semibold text-base mb-3">
                                    Kullanıcı Adı
                                </Text>
                                <View className="bg-slate-800 rounded-xl border border-slate-700">
                                    <TextInput
                                        className="text-white p-4 text-base"
                                        placeholder="Kullanıcı adınız"
                                        placeholderTextColor="#64748b"
                                        value={username}
                                        onChangeText={setUsername}
                                        autoCapitalize="none"
                                        autoCorrect={false}
                                        editable={!isLoading && !isTestingConnection}
                                    />
                                </View>
                            </View>

                            {/* Password Input */}
                            <View>
                                <Text className="text-white font-semibold text-base mb-3">
                                    Şifre
                                </Text>
                                <View className="bg-slate-800 rounded-xl border border-slate-700">
                                    <TextInput
                                        className="text-white p-4 text-base"
                                        placeholder="Şifreniz"
                                        placeholderTextColor="#64748b"
                                        value={password}
                                        onChangeText={setPassword}
                                        secureTextEntry
                                        autoCapitalize="none"
                                        autoCorrect={false}
                                        editable={!isLoading && !isTestingConnection}
                                    />
                                </View>
                            </View>

                            {/* Test Connection Button */}
                            <TouchableOpacity
                                className={`rounded-xl p-4 flex-row items-center justify-center ${
                                    isTestingConnection ? 'bg-orange-800' : 'bg-orange-600'
                                } ${(!serverUrl.trim() || !username.trim() || !password.trim()) ? 'opacity-50' : ''}`}
                                onPress={testConnection}
                                disabled={isTestingConnection || isLoading || !serverUrl.trim() || !username.trim() || !password.trim()}
                            >
                                {isTestingConnection ? (
                                    <Ionicons name="hourglass-outline" size={20} color="white" />
                                ) : (
                                    <Ionicons name="checkmark-circle-outline" size={20} color="white" />
                                )}
                                <Text className="text-white font-semibold text-base ml-2">
                                    {isTestingConnection ? 'Test Ediliyor...' : 'Bağlantıyı Test Et'}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </ScrollView>

                {/* Bottom Action Buttons */}
                <View className="px-6 py-4 bg-slate-900 border-t border-slate-800">
                    <View className="flex-row gap-3">
                        <TouchableOpacity
                            className="flex-1 bg-slate-700 rounded-xl p-4 flex-row items-center justify-center"
                            onPress={() => navigation.goBack()}
                            disabled={isLoading || isTestingConnection}
                        >
                            <Text className="text-slate-300 font-semibold text-base">İptal</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            className={`flex-1 rounded-xl p-4 flex-row items-center justify-center ${
                                isLoading || isTestingConnection ? 'bg-purple-800' : 'bg-purple-600'
                            }`}
                            onPress={handleSave}
                            disabled={isLoading || isTestingConnection}
                        >
                            {isLoading ? (
                                <ActivityIndicator size="small" color="white" />
                            ) : (
                                <Ionicons name="save-outline" size={20} color="white" />
                            )}
                            <Text className="text-white font-semibold text-base ml-2">
                                {isLoading ? 'Kaydediliyor...' : 'Kaydet'}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
};

export default AddXtreamScreen;
