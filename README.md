# NSM IPTV Player

Next Stream Media tarafından geliştirilen modern ve kullanıcı dostu IPTV oynatıcısı.

## Özellikler

### 📺 Çoklu Format Desteği
- **M3U Playlist Desteği**: HTTP/HTTPS M3U dosyalarını destekler
- **Xtream Codes API**: Popüler IPTV sağlayıcı API'si ile entegrasyon
- **Otomatik Kanal Algılama**: Playlist'lerden kanalları otomatik olarak çıkarır

### 🎮 Gelişmiş Video Oynatıcı
- **Tam Ekran Oynatma**: Landscape ve portrait mod desteği
- **Dokunmatik Kontroller**: Dokunarak kontrolleri göster/gizle
- **Kanal Değiştirme**: Hızlı kanal değiştirme butonları
- **Ses Kontrolü**: Ses seviyesi ayarlama ve sessiz modu
- **Otomatik Oynatma**: Kanal seçildiğinde otomatik oynatma

### 🔍 Akıllı Kanal Yönetimi
- **Arama Fonksiyonu**: Kanal adına göre hızlı arama
- **Grup Filtreleme**: Kanalları kategorilere göre filtreleme
- **Favori Kanallar**: Beğendiğiniz kanalları favorilere ekleme
- **Son İzlenenler**: İzleme geçmişi takibi
- **Kanal Logoları**: Otomatik logo gösterimi

### ⚙️ Kişiselleştirme
- **Tema Desteği**: Koyu tema (varsayılan)
- **Kullanıcı Tercihleri**: Video kalitesi, otomatik oynatma vb.
- **Veri Yönetimi**: Playlist'ler, favoriler ve ayarları yönetme
- **Çoklu Dil**: Türkçe arayüz

### 📱 Mobil Optimizasyonu
- **React Native**: iOS ve Android için native performans
- **Responsive Tasarım**: Tüm ekran boyutlarına uyumlu
- **Gesture Desteği**: Dokunmatik hareketlerle kontrol
- **Ekran Açık Tutma**: Video izlerken ekranın kapanmasını engelleme

## Kurulum

### Gereksinimler
- Node.js 18+
- Expo CLI
- iOS Simulator (iOS için) veya Android Emulator (Android için)

### Adımlar

1. **Projeyi klonlayın**
```bash
git clone <repository-url>
cd nsm-iptv-player
```

2. **Bağımlılıkları yükleyin**
```bash
npm install
```

3. **Uygulamayı başlatın**
```bash
npm start
```

4. **Platform seçin**
- iOS için: `i` tuşuna basın
- Android için: `a` tuşuna basın
- Web için: `w` tuşuna basın

## Kullanım

### M3U Playlist Ekleme
1. Ana ekranda "M3U Playlist Ekle" butonuna tıklayın
2. Playlist adını girin
3. M3U URL'sini girin
4. "Kaydet" butonuna tıklayın

### Xtream Codes Ekleme
1. Ana ekranda "Xtream Codes API" butonuna tıklayın
2. Playlist adını girin
3. Sunucu URL'sini girin
4. Kullanıcı adı ve şifrenizi girin
5. "Bağlantıyı Test Et" ile doğrulayın
6. "Kaydet" butonuna tıklayın

### Kanal İzleme
1. Playlist'inizden birini seçin
2. İzlemek istediğiniz kanalı seçin
3. Video oynatıcı otomatik olarak açılır
4. Ekrana dokunarak kontrolleri göster/gizle
5. Kanal değiştirme butonlarını kullanın

## Teknik Detaylar

### Kullanılan Teknolojiler
- **React Native**: Mobil uygulama framework'ü
- **Expo**: Development ve build platform'u
- **TypeScript**: Type-safe JavaScript
- **NativeWind**: Tailwind CSS for React Native
- **Expo Video**: Modern video oynatıcı
- **AsyncStorage**: Yerel veri depolama
- **React Navigation**: Sayfa yönlendirme

### Proje Yapısı
```
nsm-iptv-player/
├── screens/           # Uygulama ekranları
│   ├── home/         # Ana ekran
│   ├── channels/     # Kanal listesi
│   ├── player/       # Video oynatıcı
│   ├── add-m3u/      # M3U ekleme
│   ├── add-xtream/   # Xtream ekleme
│   ├── favorites/    # Favoriler
│   ├── recently-watched/ # Son izlenenler
│   └── settings/     # Ayarlar
├── utils/            # Yardımcı fonksiyonlar
│   ├── m3u-parser.ts # M3U parsing
│   ├── xtream-api.ts # Xtream API
│   ├── storage.ts    # Veri depolama
│   └── epg.ts        # EPG yönetimi
├── types/            # TypeScript tipleri
└── assets/           # Görseller ve ikonlar
```

### API Referansı

#### M3U Parser
```typescript
// M3U dosyasını parse etme
const playlist = await M3UParser.fetchAndParseM3U(url);

// Kanal arama
const results = M3UParser.searchChannels(channels, query);

// Grup filtreleme
const filtered = M3UParser.filterChannelsByGroup(channels, group);
```

#### Xtream API
```typescript
// Kimlik doğrulama
const api = new XtreamAPI(credentials);
const authInfo = await api.authenticate();

// Canlı kanalları getirme
const channels = await api.getLiveStreams();

// Kategorileri getirme
const categories = await api.getLiveCategories();
```

#### Storage
```typescript
// Playlist ekleme
await PlaylistStorage.addPlaylist(name, url);
await PlaylistStorage.addXtreamPlaylist(name, credentials);

// Favori ekleme
await FavoriteStorage.addFavorite(channel, playlistId);

// Son izlenen ekleme
await RecentlyWatchedStorage.addRecentlyWatched(channel, playlistId);
```

## Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakın.

## Destek

Herhangi bir sorun yaşarsanız veya öneriniz varsa:
- GitHub Issues kullanın
- E-posta: <EMAIL>

## Geliştirici

**Next Stream Media**
- Website: https://nextstreamedia.com
- GitHub: @nextstreamedia

---

Made with ❤️ by Next Stream Media
